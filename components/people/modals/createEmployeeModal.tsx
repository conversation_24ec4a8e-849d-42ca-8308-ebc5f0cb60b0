import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import CreatableSingleSelect from '@/components/common/creatableSelect';
import {
  Di<PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
import { IDepartment } from '@/interfaces/department';
import { TEmployeeData } from '@/interfaces/people';
import { IProcess } from '@/interfaces/process';
import { transformList } from '@/utils/general';
import { removeEmptyFields } from '@/utils/removeEmptyFields';
import { IUser } from '@/interfaces/user';

export const createEmployeeSchema = {
  employee_id: z.string().nonempty('Employee ID is required'),
  name: z.string().nonempty('Name is required'),
  job_title: z.object({
    name: z.string(),
  }),
  status: z.string().nonempty('Status is required'),
  date_of_joining: z.string().nonempty('Date of Joining is required'),
  supervisor_id: z.string().nonempty('Supervisor is required'),
};

interface IData extends Record<string, unknown> {
  employee_id: string;
  name: string;
  email: string | undefined;
  departments: IOption[];
  processes: IOption[];
  job_title: { id?: string; name: string } | undefined;
  status: string;
  date_of_joining: string;
  supervisor_id: string;
}

const CreateEmployeeModal = ({
  edit,
  employeeTitle,
  employeeData,
  setOpenEdit,
  reFetch,
}: {
  edit?: boolean;
  employeeTitle?: { id: string; name: string }[];
  employeeData?: TEmployeeData;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);

  const { data: departments, isLoading: deptLoading } = useFetch<{
    records: IDepartment[];
  }>(accessToken, `departments`);
  const { data: employees, isLoading: userLoading } = useFetch<{
    records: TEmployeeData[];
  }>(accessToken, `employees`);
  const { data: processes, isLoading: processLoading } = useFetch<{
    records: IProcess[];
  }>(accessToken, `processes`);

  const { putData, isLoading } = usePut();
  const {
    postData,
    isLoading: createIsloading,
    response: createResponse,
    error: createError,
  } = usePost();
  const [sameNameError, setNameError] = useState(false);

  const [data, setData] = useState<IData>({
    employee_id: '',
    name: '',
    email: '',
    departments: [],
    processes: [],
    job_title: undefined,
    status: '',
    date_of_joining: '',
    supervisor_id: '',
  });
  const [error, setError] = useState<Record<string, string> | undefined>();

  const processesData = processes?.records?.map((e) => ({
    label: e.name,
    value: e.id,
  })) as IOption[];

  const departmentData = departments?.records?.map((e) => ({
    label: e.name,
    value: e.id,
  })) as IOption[];

  const usersData = employees?.records?.map((e) => ({
    label: e.name,
    value: e.id,
  })) as IOption[];

  const { validationErrors, startValidation, reset } = useValidators({
    schemas: createEmployeeSchema,
    values: data,
  });

  const handleSubmit = async () => {
    setError(undefined);
    const payload = {
      ...data,
      departments: data.departments
        ? data.departments.map((option) => option.value)
        : [],
      processes: data.processes
        ? data.processes.map((option) => option.value)
        : [],
    };
    const { hasValidationErrors } = await startValidation();
    if (!hasValidationErrors) {
      if (edit && employeeData) {
        await putData(
          accessToken as string,
          `employees/${employeeData.id}`,
          removeEmptyFields(payload),
        );
      } else {
        await postData(
          accessToken as string,
          'employees',
          removeEmptyFields(payload),
        );
      }
      if (edit) {
        if (setOpenEdit) setOpenEdit(false);
        if (reFetch) reFetch();
      }
    } else {
      // setError(errors);
    }
  };

  const handleJobTitle = (selected: IOption) => {
    if (selected?.__isNew__) {
      setData((prev) => ({ ...prev, job_title: { name: selected?.label } }));
    } else {
      setData((prev) => ({
        ...prev,
        job_title: { name: selected?.label, id: selected?.value },
      }));
    }
  };

  useEffect(() => {
    if (edit && employeeData) {
      setData({
        employee_id: employeeData?.employee_id,
        email: employeeData?.email,
        name: employeeData?.name,
        departments: transformList(employeeData?.departments),
        processes: transformList(employeeData?.processes),
        job_title: employeeData?.job_title,
        status: employeeData?.status,
        date_of_joining: employeeData?.date_of_joining,
        supervisor_id: employeeData?.supervisor?.id || '',
      });
    }
  }, [employeeData, edit]);

  useEffect(() => {
    if (createResponse) {
      if (setOpenEdit) setOpenEdit(false);
      if (reFetch) reFetch();
    }
  }, [createResponse]);

  useEffect(() => {
    setNameError(false);
  }, [data.name]);

  return (
    <DialogContent className="max-w-[55vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle> {edit ? 'Edit' : 'Create'} Employee</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="employee_id"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Employee ID<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter Employee ID"
              id="employeeID"
              type="text"
              name="employee_id"
              value={data?.employee_id}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, employee_id: e.target.value }))
              }
              errorMsg={validationErrors?.employee_id[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="title"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Employee Name<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="name"
              defaultValue={data?.name}
              placeholder="Enter Employee name"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
              errorMsg={
                validationErrors?.name[0] ??
                (sameNameError &&
                  `Employee with name ${data.name} already exists`)
              }
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="email"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Email
            </Label>
            <Input
              type="text"
              name="email"
              defaultValue={data?.email}
              placeholder="Enter email"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, email: e.target.value }))
              }
              errorMsg={error?.name}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="job_title"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Job Title<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter Job title"
              id="job_title"
              type="text"
              name="job_title"
              value={data?.job_title?.name}
              required
              onChange={(e) =>
                setData((prev) => ({
                  ...prev,
                  job_title: { name: e.target.value },
                }))
              }
              errorMsg={validationErrors?.employee_id[0]}
            />

            {/*<Select
              value={JSON.stringify(data.job_title)}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, job_title: JSON.parse(value) }));
              }}
            >
              <SelectTrigger
                className={
                  validationErrors?.job_title[0] ? 'border-red-200' : ''
                }
                id="job_title"
              >
                <SelectValue placeholder="Enter employee job title" />
              </SelectTrigger>
              <SelectContent>
                {employeeTitle?.map((e, i) => (
                  <SelectItem value={JSON.stringify(e)} key={i}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select> */}
            {/* <CreatableSingleSelect
              placeholder="Enter job title"
              onChange={handleJobTitle}
              selectedOption={
                data.job_title
                  ? ({
                      label: data.job_title?.name,
                      value: data.job_title?.id,
                    } as IOption)
                  : undefined
              }
              endpoint="employee/titles"
              hasError={Boolean(validationErrors?.job_title[0])}
            /> */}
            {validationErrors?.job_title[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.job_title[0]}
              </div>
            ) : (
              <></>
            )}
          </div>

          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="date_of_joining"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Date of Joining<span className="text-red-200">*</span>
            </Label>

            <Calendar
              selectedDate={data?.date_of_joining}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    date_of_joining: moment(date as string).format(
                      'YYYY-MM-DD',
                    ),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    date_of_joining: '',
                  }));
                }
              }}
              className={
                validationErrors?.date_of_joining[0]
                  ? 'border !border-red-200'
                  : ''
              }
              allowPastDates
            />
            {validationErrors?.date_of_joining[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200 mt-2.5">
                {validationErrors?.date_of_joining[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="department"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Departments
          </Label>
          <ReactSelectMulti
            value={data.departments}
            options={departmentData}
            placeholder="Select departments"
            onChange={(value) => {
              setData((pre) => ({
                ...pre,
                departments: value as IOption[],
              }));
            }}
          />
        </div>

        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="processes"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Processes
          </Label>
          <ReactSelectMulti
            value={data.processes}
            options={processesData}
            placeholder="Select processes"
            onChange={(value) => {
              setData((pre) => ({
                ...pre,
                processes: value as IOption[],
              }));
            }}
          />
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="status"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Status<span className="text-red-200">*</span>
            </Label>
            <Select
              value={data.status}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, status: value }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.status[0] ? 'border-red-200' : ''}
                id="status"
              >
                <SelectValue placeholder="Enter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="On vacation">On vacation</SelectItem>
                <SelectItem value="On duty">On duty</SelectItem>
                <SelectItem value="Suspended">Suspended</SelectItem>
                <SelectItem value="Terminated">Terminated</SelectItem>
                <SelectItem value="Resigned">Resigned</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.status[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.status[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="supervisor"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Supervisor<span className="text-red-200">*</span>
            </Label>
            <Select
              value={data.supervisor_id}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, supervisor_id: value }));
              }}
            >
              <SelectTrigger
                className={
                  validationErrors?.supervisor_id[0] ? 'border-red-200' : ''
                }
                id="supervisor"
              >
                <SelectValue placeholder="Select supervisor" />
              </SelectTrigger>
              <SelectContent>
                {usersData?.map((user) => (
                  <SelectItem key={user.value} value={user.value}>
                    {user.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors?.supervisor_id[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.supervisor_id[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex justify-end">
          <PrimaryButton
            size="medium"
            text="Submit"
            onClick={handleSubmit}
            isLoading={isLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateEmployeeModal;
