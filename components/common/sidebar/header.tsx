import React from 'react';

import { useAuthStore } from '@/globalProvider/authStore';

import MockUserSwitcher from './components/MockUserSwitch';
import OrgSwitcher from './components/OrgSwitch';
import OrganizationIcon from '@/assets/outline/organization';
import { useRouter } from 'next/router';

const Header = () => {
  const user = useAuthStore((state) => state.user);
  const router = useRouter();

  return (
    <div className="flex justify-between items-center pb-4 border-b border-grey-100">
      <div className="flex items-center gap-2.5 h-9">
        <OrganizationIcon />
        <span className="font-medium text-2xl leading-9 text-dark-300">
          {user?.company.name}
        </span>
      </div>
      <div className="flex gap-8 items-center">
        {(process.env.NEXT_PUBLIC_ENVIRONMENT === 'dev' ||
          process.env.NEXT_PUBLIC_ENVIRONMENT === 'local') && (
          <MockUserSwitcher />
        )}
        <OrgSwitcher />
      </div>
      {/* <div className="flex flex-col gap-1">
        <div className="font-semibold text-black leading-6 text-base">
          <PERSON>
        </div>
        <div className="text-grey-200 text-sm leading-5 font-medium">
          <EMAIL>
        </div>
      </div> */}
    </div>
  );
};

export default Header;
