import React, { useState } from 'react';
import { Pencil, TrashIcon } from 'lucide-react';
import { Dialog } from '../common/dialog';
import CreateSkillLevelModal from './createSkillLevelModal';
import DeleteModal from '../common/modals/deleteModal';
import { useDelete } from '@/hooks/useDelete';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/globalProvider/authStore';
import { toast } from 'react-toastify';

interface SkillLevel {
  id: string;
  level: string;
  name: string;
  description: string;
  requirements: string[];
}

interface SkillsLevelCardsProps {
  data: SkillLevel[];
  onEdit: (level: SkillLevel) => void;
  reFetch: () => void;
}

const SkillsLevelCards: React.FC<SkillsLevelCardsProps> = ({
  data,
  onEdit,
  reFetch,
}) => {
  // State for edit modal
  const [editingLevel, setEditingLevel] = useState<SkillLevel | null>(null);
  // State for delete modal
  const [deletingLevel, setDeletingLevel] = useState<SkillLevel | null>(null);

  const { deleteData, isLoading } = useDelete();
  const router = useRouter();
  const { accessToken } = useAuthStore();

  const handleDelete = async (id: string) => {
    await deleteData(accessToken as string, `employee/skill-levels/${id}`);
    setDeletingLevel(null);
    reFetch();
    toast.success('Skill level deleted successfully');
  };

  return (
    <>
      {/* Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.map((level) => (
          <div
            key={level.id}
            className="relative p-6 rounded-lg border bg-white transition-shadow duration-200 flex justify-between items-start"
          >
            <div className="space-y-2">
              <h3 className="text-lg font-bold text-dark-300">{level.name}</h3>
              <p className="text-base text-gray-700 leading-relaxed">
                {level.description}
              </p>
              <ul className="text-sm text-gray-700 space-y-1">
                {level.requirements.map((requirement, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 bg-gray-700 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="leading-relaxed">{requirement}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Actions */}
            <div className="flex gap-2 items-end">
              <button
                onClick={() => setEditingLevel(level)}
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                title="Edit Level"
              >
                <Pencil size={16} className="text-gray-600" />
              </button>
              <button
                onClick={() => setDeletingLevel(level)}
                className="w-8 h-8 bg-red-50 rounded-full flex items-center justify-center hover:bg-red-100 transition-colors"
                title="Delete Level"
              >
                <TrashIcon size={16} className="text-red-500" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Delete Modal */}
      <Dialog
        open={!!deletingLevel}
        onOpenChange={() => setDeletingLevel(null)}
      >
        {deletingLevel && (
          <DeleteModal
            title="Delete Skill Level"
            infoText={`Are you sure you want to delete "${deletingLevel.name}"?`}
            btnText="Delete"
            btnLoading={isLoading}
            onClick={() => handleDelete(deletingLevel.id)}
          />
        )}
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={!!editingLevel} onOpenChange={() => setEditingLevel(null)}>
        {editingLevel && (
          <CreateSkillLevelModal
            open={!!editingLevel}
            onOpenChange={() => setEditingLevel(null)}
            edit
            skillData={editingLevel}
            setOpenEdit={() => setEditingLevel(null)}
            levelId={editingLevel.id}
            reFetch={reFetch}
          />
        )}
      </Dialog>
    </>
  );
};

export default SkillsLevelCards;
