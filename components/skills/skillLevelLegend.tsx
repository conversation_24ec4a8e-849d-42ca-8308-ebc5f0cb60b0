import React from 'react';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';

const SkillLevelLegend = ({ skillLevels }: { skillLevels: any[] }) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h3 className="text-lg font-semibold text-gray-700">
        Skill Matrix Legend
      </h3>
      <p className="text-dark-100 font-normal text-base leading-8">
        This matrix shows employee skill levels across different categories.
        Click any cell to edit skill assessments.
      </p>
      <div className="flex flex-wrap gap-4 mt-2">
        {skillLevels.map((level) => {
          const colors = getSkillLevelColor(level.name);
          return (
            <div key={level.id} className="flex items-center gap-2">
              <span
                className={`px-2 py-1 rounded-full text-xs ${colors.bg} text-white font-medium`}
              >
                {level.name}
              </span>
              <span className="text-sm text-gray-600">
                {/* {getSkillLevelName(level.name)} */}
                {level.description}
              </span>
            </div>
          );
        })}
        <div className="flex items-center gap-2">
          <span className="px-2 py-1 rounded-full text-xs bg-white-100 text-grey-300 border border-dashed border-grey-200">
            +
          </span>
          <span className="text-sm text-gray-600">Not Assigned</span>
        </div>
      </div>
    </div>
  );
};

export default SkillLevelLegend;
