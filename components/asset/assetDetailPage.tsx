import axios from 'axios';
import { ArrowLeft, Download, Edit, MoreHorizontal, Plus, Trash2 } from 'lucide-react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import DeleteIcon from '@/assets/outline/delete';
import EditIcon from '@/assets/outline/edit';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import { DetailsTextNew } from '@/components/common/infoDetail';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import Status from '@/components/common/status';
import CommonTable from '@/components/common/table';
import { AccessActions } from '@/constants/access';
import { ORGANIZATION_HEADER_KEY, ORGANIZATION_SESSION_KEY } from '@/constants/common';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { getValueOrDefault } from '@/utils/general';
import { getFileNameFromPath } from '@/utils/helper';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate } from '@/utils/time';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';

import DeleteButton from '../common/button/deleteButton';
import LinkButton from '../common/button/linkButton';
import DocumentViewModal from '../common/modals/documentViewModal';
import { CalibrationModal } from './modals/calibrationModal';
import CreateAssetModal from './modals/createAssetModal';

interface TCustomColDef extends ColDef {
  headerName: string;
  field: string;
  sortable: boolean;
  resizable: boolean;
  getQuickFilterText: (params: unknown | any) => string;
  valueFormatter: (params: ValueFormatterParams) => string;
  filter: boolean;
}

interface ICalibrationRecord {
  id: string;
  calibration_date: string;
  calibration_cert_num: string;
  outcome: string;
  comment: string;
  created_at: string;
  file_name: string;
  file_path: string;
  document_type: string;
  document_id?: string;
}

interface IAssetDetailData {
  purchase_date: string;
  id: string;
  asset_id: string;
  name: string;
  description: string;
  location: string;
  status: string;
  owner: { id: string; name: string; full_name?: string };
  calibration: boolean;
  calibration_date?: string;
  calibration_cert_num?: string;
  calibration_period?: number;
  calibrations?: ICalibrationRecord[];
  justification?: string;
  last_calibration_date?: string;
  next_calibration_date?: string;
}

const AssetDetailPage = () => {
  const { accessToken, user } = useAuthStore();
  const [openEditModal, setOpenEditModal] = useState(false);
  const [openCalibrationModal, setOpenCalibrationModal] = useState(false);
  const { deleteData, isLoading: deleteLoading } = useDelete();
  const {
    deleteData: deleteCalibrationData,
    isLoading: deleteCalibrationLoading,
  } = useDelete();
  const router = useRouter();
  const { assetId } = router.query;
  const [selectedCalibration, setSelectedCalibration] =
    useState<ICalibrationRecord | null>(null);
  const [deleteConfirmationModal, setDeleteConfirmationModal] =
    useState<boolean>(false);
  const [calibrationIdToDelete, setCalibrationIdToDelete] = useState<
    string | null
  >(null);
  const [editCalibration, setEditCalibration] = useState<boolean>(false);

  const { data, isLoading, reFetch } = useFetch<IAssetDetailData>(
    accessToken,
    assetId ? `assets/${assetId}` : undefined,
  );

  useEffect(() => {
    // Refetch when id changes
    if (assetId) {
      reFetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assetId]);

  const breadcrumbData = [
    { name: 'Asset Hub', link: '/asset' },
    { name: data?.name || '--', link: '#' },
  ];

  const handleDelete = async () => {
    if (data?.id && accessToken) {
      await deleteData(accessToken, `assets/${data.id}`);
      router.push('/asset');
    }
  };

  const handleCalibrationEdit = (calibrationData: ICalibrationRecord) => {
    setSelectedCalibration(calibrationData);
    setEditCalibration(true);
    setOpenCalibrationModal(true);
  };

  const handleCalibrationDelete = (
    calibrationData: Record<string, unknown>,
  ) => {
    setCalibrationIdToDelete(calibrationData.id as string);
    setDeleteConfirmationModal(true);
  };

  const isViewableDoc = (data: any) => {
    if (data.document_type === 'independent') {
      return ['pdf', 'docx', 'doc', 'jpg', 'png', 'jpeg'].includes(
        (data?.file_name as string)?.split('.').pop() || '',
      );
    }
    if (data.document_type === 'doc_hub') {
      return ['pdf', 'docx', 'doc', 'jpg', 'png', 'jpeg'].includes(
        (data?.document_version_data?.file_extension as string) || '',
      );
    }

    return false;
  };
  const getFilePath = (data: any) => {
    if (data.document_type === 'independent') {
      return data.file_path;
    }
    if (data.document_type === 'doc_hub') {
      return data.document_version_data.file_path;
    }
    return '';
  };

  // Using the module-level handleDownloadCalibrationDoc function

  const confirmDeleteCalibration = async () => {
    if (calibrationIdToDelete && accessToken && assetId) {
      try {
        await deleteCalibrationData(
          accessToken,
          `assets/${assetId}/calibrations/${calibrationIdToDelete}`,
        );
        toast.success('Calibration certificate deleted successfully');
        setDeleteConfirmationModal(false);
        setCalibrationIdToDelete(null);
        reFetch();
      } catch (error) {
        toast.error('Failed to delete calibration certificate');
        console.error(error);
      }
    }
  };

  // Read-only columns for when calibration is disabled but data exists
  const getReadOnlyCalibrationColumns = (): TCustomColDef[] => [
    {
      headerName: 'File name',
      field: 'file_name',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.value ? String(params.value) : '';
      },
      valueFormatter: (params: ValueFormatterParams) =>
        getValueOrDefault(params.data, 'file_name') || '',
      filter: false,
      cellRenderer: (params: {
        data: { file_name: string; [key: string]: any };
      }) => {
        return isViewableDoc(params.data) ? (
          <Dialog>
            <DialogTrigger asChild>
              <div className="text-primary-400 font-medium cursor-pointer">
                <span>{params.data.file_name}</span>
              </div>
            </DialogTrigger>
            <DocumentViewModal
              title={params.data.file_name}
              filePath={getFilePath(params.data)}
              extension={
                getFilePath(params.data)?.split('.').pop() as
                  | 'html'
                  | 'pdf'
                  | 'png'
                  | 'jpeg'
                  | 'jpg'
              }
              dialogClass="min-w-[95%]"
            />
          </Dialog>
        ) : (
          <span>{params.data.file_name}</span>
        );
      },
    },
    {
      headerName: 'Certificate No.',
      field: 'calibration_cert_num',
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => params.value || '',
      getQuickFilterText: (params: any) => params.value || '',
      filter: true,
      width: 150,
    },
    {
      headerName: 'Calibration date',
      field: 'calibration_date',
      sortable: true,
      resizable: true,
      valueFormatter: (params: any) => formatDate(params.value),
      getQuickFilterText: (params: any) =>
        params.value ? String(formatDate(params.value)) : '',
      filter: true,
      width: 150,
    },
    {
      headerName: 'Outcome',
      field: 'outcome',
      sortable: false,
      resizable: true,
      valueGetter: (params: any) => params.value?.toLowerCase(),
      valueFormatter: (params: any) => params.value || '',
      cellRenderer: (params: any) => (
        <div className="flex items-center h-full">
          <Status type={params.value} />
        </div>
      ),
      getQuickFilterText: (params: any) => params.value || '',
      filter: true,
      width: 120,
    },
    {
      headerName: 'Comments',
      field: 'comment',
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => params.value || '--',
      getQuickFilterText: (params: any) => params.value || '',
      filter: true,
      width: 200,
    },
    {
      headerName: 'View',
      field: 'actions',
      sortable: false,
      resizable: false,
      cellRenderer: (params: any) => (
        <div className="flex justify-center items-center gap-2 h-[100%]">
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleDownloadCalibrationDoc(
                params.data?.file_path as string,
                params.data?.file_name as string,
              );
            }}
            className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
            title="Download"
          >
            <Download height={'20'} width="20" />
          </button>
        </div>
      ),
      getQuickFilterText: (params: any) =>
        params.value ? String(params.value) : '',
      filter: false,
      width: 100,
      valueFormatter: (params: ValueFormatterParams) =>
        params.value ? String(params.value) : '--',
    },
  ];

  const getCalibrationColumns = (): TCustomColDef[] => [
    {
      headerName: 'File name',
      field: 'file_name',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.value ? String(params.value) : '';
      },
      valueFormatter: (params: ValueFormatterParams) =>
        getValueOrDefault(params.data, 'file_name') || '',
      filter: false,
      cellRenderer: (params: {
        data: { file_name: string; [key: string]: any };
      }) => {
        return isViewableDoc(params.data) ? (
          <Dialog>
            <DialogTrigger asChild>
              <div className="text-primary-400 font-medium cursor-pointer">
                <span>{params.data.file_name}</span>
              </div>
            </DialogTrigger>
            <DocumentViewModal
              title={params.data.file_name}
              filePath={getFilePath(params.data)}
              extension={
                getFilePath(params.data)?.split('.').pop() as
                  | 'html'
                  | 'pdf'
                  | 'png'
                  | 'jpeg'
                  | 'jpg'
              }
              dialogClass="min-w-[95%]"
            />
          </Dialog>
        ) : (
          <span>{params.data.file_name}</span>
        );
      },
    },
    {
      headerName: 'Certificate No.',
      field: 'calibration_cert_num',
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => params.value || '',
      getQuickFilterText: (params: any) => params.value || '',
      filter: true,
      width: 150,
    },
    {
      headerName: 'Calibration date',
      field: 'calibration_date',
      sortable: true,
      resizable: true,
      valueFormatter: (params: any) => formatDate(params.value),
      getQuickFilterText: (params: any) =>
        params.value ? String(formatDate(params.value)) : '',
      filter: true,
      width: 150,
    },
    {
      headerName: 'Outcome',
      field: 'outcome',
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => params.value || '',
      cellRenderer: (params: any) => (
        <div className="flex items-center h-full">
          <Status type={params.value} />
        </div>
      ),
      getQuickFilterText: (params: any) => params.value || '',
      filter: true,
      width: 120,
    },
    {
      headerName: 'Comments',
      field: 'comment',
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) => params.value || '--',
      getQuickFilterText: (params: any) => params.value || '',
      filter: true,
      width: 200,
    },
    {
      headerName: 'Manage',
      field: 'actions',
      sortable: false,
      resizable: false,
      cellRenderer: (params: any) => (
        <CalibrationManageCellRenderer
          rowData={params.data}
          handleEdit={handleCalibrationEdit}
          handleDelete={handleCalibrationDelete}
          handleDownloadDoc={handleDownloadCalibrationDoc}
          hasAccessEditDelete={hasAccess(
            AccessActions.CanAddOrEditAssets,
            user,
          )}
        />
      ),
      getQuickFilterText: (params: any) =>
        params.value ? String(params.value) : '',
      filter: false,
      width: 140,
      valueFormatter: (params: any) =>
        params.value ? String(params.value) : '--',
    },
  ];

  return (
    <Layout>
      {isLoading ? (
        <Loader className="h-[400px]" />
      ) : (
        <div className="flex flex-col gap-6">
          {/* Asset Info */}
          <div className="flex flex-col flex-1">
            <div className=" my-5">
              <div className="flex items-center gap-4">
                <SecondaryButton
                  icon={<ArrowLeft />}
                  onClick={() => router.push('/asset')}
                  text=""
                  size="medium"
                  buttonClasses="!px-2.5"
                />
                <div>
                  <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                    {data?.name}
                  </div>
                </div>
              </div>
            </div>

            <div className="border border-grey-100 bg-white p-2 rounded-lg">
              <div className="flex items-start justify-between ">
                <div className="p-2 flex flex-col gap-3">
                  <DetailsTextNew
                    label="Asset ID"
                    value={data?.asset_id || '--'}
                  />
                  <DetailsTextNew
                    label="Owner"
                    value={data?.owner?.name || '--'}
                  />
                </div>
                <div className="p-2 flex flex-col gap-3">
                  <DetailsTextNew label="Status" value={data?.status || '--'} />

                  <DetailsTextNew
                    label="Location"
                    value={data?.location || '--'}
                  />
                </div>
                {/* Only show calibration dates if calibration is required */}
                {data?.calibration && (
                  <div className="p-2 flex flex-col gap-3">
                    <DetailsTextNew
                      label="Last Calibration Date"
                      value={data?.last_calibration_date || '--'}
                    />

                    <DetailsTextNew
                      label="Next Calibration Date"
                      value={data?.next_calibration_date || '--'}
                    />
                  </div>
                )}

                {hasAccess(AccessActions.IsAssetAdmin, user) && (
                  <div className="flex items-center gap-3">
                    <Dialog
                      open={openEditModal}
                      onOpenChange={setOpenEditModal}
                    >
                      <DialogTrigger asChild>
                        <SecondaryButton
                          size="medium"
                          icon={<Edit color="#016366" className="h-5 w-5" />}
                          text="Edit"
                        />
                      </DialogTrigger>
                      <CreateAssetModal
                        edit
                        assetData={data as any}
                        setOpenEdit={setOpenEditModal}
                        reFetch={reFetch}
                      />
                    </Dialog>

                    <Dialog>
                      <DialogTrigger asChild>
                        <DeleteButton text="Delete" />
                      </DialogTrigger>
                      <DeleteModal
                        title="Delete Asset"
                        infoText="Are you sure you want to delete this asset?"
                        btnText="Delete"
                        onClick={handleDelete}
                        btnLoading={deleteLoading}
                        dialogContentClass="min-w-[28.5rem]"
                      >
                        <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium leading-5 text-grey-300">
                              Asset Name
                            </div>
                            <div className="text-base font-medium leading-6 text-dark-300">
                              {data?.name || '--'}
                            </div>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium leading-5 text-grey-300">
                              Asset ID
                            </div>
                            <div className="text-base font-medium leading-6 text-dark-300">
                              {data?.asset_id || '--'}
                            </div>
                          </div>
                        </div>
                      </DeleteModal>
                    </Dialog>
                  </div>
                )}
                {!hasAccess(AccessActions.IsAssetAdmin, user) &&
                  hasAccess(AccessActions.CanAddOrEditAssets, user) && (
                    <div className="flex items-center gap-3">
                      <Dialog
                        open={openEditModal}
                        onOpenChange={setOpenEditModal}
                      >
                        <DialogTrigger asChild>
                          <SecondaryButton
                            size="medium"
                            icon={<Edit color="#016366" className="h-5 w-5" />}
                            text="Edit"
                          />
                        </DialogTrigger>
                        <CreateAssetModal
                          edit
                          assetData={data as any}
                          setOpenEdit={setOpenEditModal}
                          reFetch={reFetch}
                        />
                      </Dialog>
                    </div>
                  )}
              </div>
            </div>
          </div>

          {/* Calibration History */}
          {data?.calibration ||
          (data?.calibrations && data?.calibrations.length > 0) ? (
            <div className="bg-white rounded-lg p-6 shadow-shadow-1">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <div className="text-xl font-semibold leading-7 text-dark-300">
                    Calibration History
                  </div>
                  <div className="text-sm text-gray-500 mb-2">
                    Record the calibration status of tools or equipment used
                  </div>
                </div>
                {/* Only show Add Certificate button if calibration is required */}
                {data?.calibration &&
                  hasAccess(AccessActions.CanAddOrEditAssets, user) && (
                    <div>
                      <Dialog
                        open={openCalibrationModal}
                        onOpenChange={setOpenCalibrationModal}
                      >
                        <DialogTrigger asChild>
                          <LinkButton
                            size="medium"
                            icon={<Plus className="h-4 w-4" />}
                            text="Add New Certificate"
                            onClick={() => {
                              setEditCalibration(false);
                              setSelectedCalibration(null);
                            }}
                          />
                        </DialogTrigger>
                        <CalibrationModal
                          setShowModal={setOpenCalibrationModal}
                          showModal={openCalibrationModal}
                          assetId={assetId as string}
                          edit={editCalibration}
                          calibrationData={selectedCalibration || undefined}
                          calibrationId={selectedCalibration?.id}
                          onSuccess={() => {
                            setOpenCalibrationModal(false);
                            setSelectedCalibration(null);
                            setEditCalibration(false);
                            reFetch();
                          }}
                        />
                      </Dialog>
                    </div>
                  )}
              </div>
              {/* Show banner when calibration is disabled but certificates exist */}
              {!data?.calibration &&
                data?.calibrations &&
                data.calibrations.length > 0 && (
                  <div className="mb-4 p-4 border border-yellow-400 bg-yellow-50 rounded-lg">
                    <p className="text-sm text-yellow-700">
                      Calibration setting for this asset has been disabled.
                      Enable calibration to add / edit certificates.
                    </p>
                  </div>
                )}

              {/* Show calibration table with appropriate columns based on calibration setting */}
              {
                <div
                  className={
                    data?.calibration === false ? 'disabled-table' : ''
                  }
                >
                  <CommonTable
                    data={{ records: data?.calibrations || [] }}
                    columnDefs={
                      data?.calibration
                        ? getCalibrationColumns()
                        : getReadOnlyCalibrationColumns()
                    }
                    searchBox={false}
                    paginate={false}
                    isLoading={false}
                  />
                </div>
              }
            </div>
          ) : (
            ''
          )}
        </div>
      )}

      {/* Delete Calibration Confirmation Modal */}
      <Dialog
        open={deleteConfirmationModal}
        onOpenChange={setDeleteConfirmationModal}
      >
        <DeleteModal
          title="Delete Calibration Certificate"
          infoText="Are you sure you want to delete this calibration certificate?"
          btnText="Delete"
          btnLoading={deleteCalibrationLoading}
          onClick={confirmDeleteCalibration}
        />
      </Dialog>
    </Layout>
  );
};

// Added handleDownloadCalibrationDoc to this module scope to be accessible by both table renderers
const handleDownloadCalibrationDoc = (path: string, documentTitle: string) => {
  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;
  const accessToken = useAuthStore.getState().accessToken;
  const orgId =
    typeof window !== 'undefined'
      ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
      : null;

  const config = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
      ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
    },
  };
  axios
    .get(
      `${baseUrl}/${productVersion}/file/presigned-url?file_path=${path}&expiration=600`,
      config,
    )
    .then((res) => {
      fetch(res.data.url)
        .then((res) => res.blob())
        .then((res) => {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(res);
          link.download = getFileNameFromPath(path, documentTitle, 0);
          link.click();
        });
    })
    .catch((err) => {
      console.log(err);
    });
};

export const CalibrationManageCellRenderer = ({
  rowData,
  handleEdit,
  handleDelete,
  handleDownloadDoc,
  hasAccessEditDelete,
}: {
  rowData: Record<string, unknown>;
  handleEdit: (rowData: ICalibrationRecord) => void;
  handleDelete: (rowData: Record<string, unknown>) => void;
  handleDownloadDoc: (path: string, documentTitle: string) => void;
  hasAccessEditDelete: boolean;
}) => {
  return (
    <div className="flex justify-center items-center gap-2 h-[100%]">
      {hasAccessEditDelete ? (
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            handleEdit(rowData as unknown as ICalibrationRecord);
          }}
          className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
          title="Edit"
        >
          <EditIcon height={'20'} width="20" />
        </button>
      ) : (
        ''
      )}

      <button
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleDownloadDoc(
            rowData?.file_path as string,
            rowData?.file_name as string,
          );
        }}
        className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
        title="Download"
      >
        <Download height={'20'} width="20" />
      </button>

      {hasAccessEditDelete ? (
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            handleDelete(rowData);
          }}
          className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
          title="Delete"
        >
          <DeleteIcon height={'20'} width="20" />
        </button>
      ) : (
        ''
      )}
    </div>
  );
};

export default AssetDetailPage;
