import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import ResetFilterIcon from '@/assets/outline/resetFilter';
import SettingIcon from '@/assets/outline/settting';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { IDocumentDetails, IDocumentSummary } from '@/interfaces/document';
import { fetchData } from '@/utils/api';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getValueOrDefault } from '@/utils/table';
import { formatDate } from '@/utils/time';

import Breadcrumb from '../common/breadcrumb';
import PrimaryButton from '../common/button/primaryButton';
import SecondaryButton from '../common/button/secondaryButton';
import TertiaryButton from '../common/button/tertiaryButton';
import { Dialog, DialogTrigger } from '../common/dialog';
import SideBarWrapper from '../common/sidebar/layout';
import CommonTable from '../common/table';
import Tabs from '../common/tabs';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import { DocumentFilterConstants } from './components/enum';
import CreateDocumentModal from './components/modals/createDocumentModal';
import AdvancedTable from '../common/advancedTable';

interface IDocumentData {
  summary: IDocumentSummary;
  records: IDocumentDetails[];
}

interface DocumentComponentProps {
  accessToken: string;
  filter: any;
}

const DocumentHub = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [createDocument, setCreateDocument] = useState(false);
  const { accessToken, user } = useAuthStore();
  const [filterKey, setFilterKey] = useState<string | undefined>(undefined);

  const [data, setData] = useState<IDocumentData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<any>(null);

  const getDocuments = async () => {
    setIsLoading(true);
    try {
      const filterValue = filterKey
        ? DocumentFilterConstants[
            filterKey as keyof typeof DocumentFilterConstants
          ]
        : {};

      const response = await fetchData(
        accessToken as string,
        'documents',
        filterValue,
      );
      setData(response?.data);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (accessToken && filterKey) getDocuments();
  }, [filterKey, accessToken]);

  const router = useRouter();

  const columns: any[] = [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      resizable: true,
      width: 350,
      showFilter: false,
      render: (value: any, row: any) => {
        return (
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-primary-400 font-semibold cursor-pointer">
                {row?.title}
              </span>
              {row?.document_version?.version_number && (
                <span className="bg-gray-100 text-gray-700 text-xs font-medium px-2 py-0.5 rounded-full">
                  v{row.document_version.version_number}
                </span>
              )}
            </div>
            {row?.doc_id && (
              <span className="text-gray-500 text-xs font-semibold">
                ID: {row.doc_id}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'department.name',
      label: 'Department',
      sortable: true,
      resizable: true,
      width: undefined,
      showFilter: true,
    },
    {
      key: 'category.name',
      label: 'Category',
      sortable: true,
      resizable: true,
      width: undefined,
      showFilter: true,
    },
    {
      key: 'processes',
      label: 'Processes',
      sortable: false,
      resizable: true,
      width: undefined,
      showFilter: true,
    },
    {
      key: 'next_review_date',
      label: 'Review date',
      sortable: true,
      resizable: true,
      width: undefined,
      showFilter: false,
      render: (value: any) => {
        if (!value || isNaN(new Date(value).getTime()))
          return (
            <div>
              <span className="text-grey-400 px-4 py-1 rounded-md">-</span>
            </div>
          );

        const reviewDate = formatDate(value, false);
        const days = moment(value).diff(new Date(), 'days');

        if (Number.isNaN(days))
          return (
            <div>
              <span className="text-grey-400 px-4 py-1 rounded-md">-</span>
            </div>
          );

        const tooltipText =
          days <= 0
            ? `${Math.abs(days)} day${Math.abs(days) !== 1 ? 's' : ''} overdue`
            : `${days} day${days !== 1 ? 's' : ''} remaining`;

        if (days <= 0) {
          return (
            <div title={tooltipText}>
              <span className="bg-red-500 bg-opacity-[15%] px-4 py-1 rounded-md">
                {reviewDate}
              </span>
            </div>
          );
        }

        if (days < 30) {
          return (
            <div title={tooltipText}>
              <span className="bg-yellow-500 bg-opacity-[15%] px-4 py-1 rounded-md">
                {reviewDate}
              </span>
            </div>
          );
        }

        return (
          <div title={tooltipText}>
            <span className="px-4 py-1 rounded-md">{reviewDate}</span>
          </div>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      resizable: true,
      width: undefined,
      showFilter: true,
    },
    {
      key: 'assignees',
      label: 'Assignee',
      sortable: false,
      resizable: true,
      width: undefined,
      showFilter: true,
    },
    {
      key: 'approvers',
      label: 'Approver',
      sortable: false,
      resizable: true,
      width: undefined,
      showFilter: true,
    },
    {
      key: 'publish_date',
      label: 'Date of publish',
      sortable: true,
      resizable: true,
      width: 150,
      showFilter: false,
      render: (value: string | null | undefined) => {
        if (!value || isNaN(new Date(value).getTime())) return '-';
        return formatDate(value, false);
      },
    },
  ];

  const breadcrumbData = [
    {
      name: 'Document Hub',
      link: '#',
    },
  ];

  const tabsData = [
    {
      name: `All docs (${data?.summary.total || 0})`,
      textColor: 'text-dark-100',
      filter: 'ALL',
      onClick: () => setFilterKey('ALL'),
    },
    {
      name: `My docs (${data?.summary.assigned_to_me_count || 0})`,
      textColor: 'text-dark-100',
      filter: 'ASSIGNED_TO_ME',
      onClick: () => setFilterKey('ASSIGNED_TO_ME'),
    },
    {
      name: `My pending approval (${
        data?.summary.my_pending_approval_count || 0
      })`,
      textColor: 'text-dark-100',
      filter: 'MY_PENDING_APPROVAL',
      onClick: () => setFilterKey('MY_PENDING_APPROVAL'),
    },
    {
      name: `Pending approval (${
        data?.summary.waiting_for_approval_count || 0
      })`,
      textColor: 'text-dark-100',
      filter: 'NEEDS_APPROVAL',
      onClick: () => setFilterKey('NEEDS_APPROVAL'),
    },
    {
      name: `Approved (${data?.summary.approved_approval_count || 0})`,
      textColor: 'text-dark-100',
      filter: 'IS_APPROVED_APPROVAL',
      onClick: () => setFilterKey('IS_APPROVED_APPROVAL'),
    },
    {
      name: `Rejected (${data?.summary.rejected_approval_count || 0})`,
      textColor: 'text-dark-100',
      filter: 'IS_REJECTED_APPROVAL',
      onClick: () => setFilterKey('IS_REJECTED_APPROVAL'),
    },
    {
      name: `Review in 30 days (${data?.summary.review_date_30d_count || 0})`,
      textColor: 'text-[#F19413]',
      filter: 'REVIEW_IN_30_DAYS',
      onClick: () => setFilterKey('REVIEW_IN_30_DAYS'),
    },
    {
      name: `Past due (${data?.summary.review_date_overdue_count || 0})`,
      textColor: 'text-[#E05252]',
      filter: 'PAST_DUE',
      onClick: () => setFilterKey('PAST_DUE'),
    },
  ];

  const handleClick = (id: string) => {
    router.push({
      pathname: `/document/${id}`,
      query: router.query,
    });
  };

  useEffect(() => {
    if (router.query.filter) {
      const queryKey = router.query.filter;
      const matchedFilterKey = Object.keys(DocumentFilterConstants).find(
        (key) => key === queryKey,
      );

      if (matchedFilterKey) {
        setFilterKey(matchedFilterKey);
      }
      const matchedTabIndex = tabsData.findIndex(
        (tab) => tab.filter === queryKey,
      );
      if (matchedTabIndex !== -1) {
        setActiveTab(matchedTabIndex);
      }
    }
  }, [router.query.filter]);

  useEffect(() => {
    if (router.isReady && filterKey !== undefined) {
      const currentQuery = router.query;
      router.push({
        pathname: '/document',
        query: {
          ...currentQuery,
          filter: filterKey,
        },
      });
    }
  }, [filterKey, router.isReady]);

  useEffect(() => {
    if (filterKey === undefined && router.isReady && !router.query.filter) {
      setFilterKey('ALL');
    }
  }, [router.isReady, router.query.filter, filterKey]);

  if (error) {
    return (
      <div className="rounded-md border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <p className="p-4">{error.response?.data?.error}</p>
      </div>
    );
  }

  return (
    <SideBarWrapper>
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Document Hub
          </div>
        </div>
      </div>
      <div className="mt-2">
        <Tabs
          tabsData={tabsData}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />
      </div>

      <div className="">
        <AdvancedTable
          data={data?.records ?? []}
          loading={isLoading}
          columns={columns}
          handleRowClicked={(row) => handleClick(row?.id)}
          sortable
          resizable
          pagination
          searchRightSideElement={
            <div className="flex gap-4">
              <Tooltip>
                <TooltipTrigger>
                  <TertiaryButton
                    icon={<ResetFilterIcon />}
                    text=""
                    size="medium"
                    buttonClasses="!p-2.5"
                    onClick={() => {
                      const query = router.query;
                      query.page = '0';
                      query.pageSize = '10';
                      router.push({
                        pathname: router.pathname,
                        query: query,
                      });
                    }}
                  />
                </TooltipTrigger>
                <TooltipContent>Reset table filter</TooltipContent>
              </Tooltip>
              {hasAccess(AccessActions.IsDocumentAdmin, user) && (
                <Tooltip>
                  <TooltipTrigger>
                    <Link href={'/document-administration'}>
                      <SecondaryButton
                        icon={<SettingIcon />}
                        text=""
                        size="medium"
                        buttonClasses="!p-2.5"
                      />
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>Document Administration</TooltipContent>
                </Tooltip>
              )}

              {hasAccess(AccessActions.CanEditDocument, user) && (
                <Dialog open={createDocument} onOpenChange={setCreateDocument}>
                  <DialogTrigger asChild>
                    <PrimaryButton
                      text="New Document"
                      buttonClasses="!px-5 !py-2"
                    />
                  </DialogTrigger>
                  <CreateDocumentModal
                    reFetch={getDocuments}
                    open={createDocument}
                    setOpenEdit={setCreateDocument}
                  />
                </Dialog>
              )}
            </div>
          }
        />
      </div>
    </SideBarWrapper>
  );
};

export default DocumentHub;
