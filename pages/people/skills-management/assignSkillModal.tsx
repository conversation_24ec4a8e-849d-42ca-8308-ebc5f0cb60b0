import React, { useState, useEffect, useMemo, useCallback } from 'react';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';
import { usePost } from '@/hooks/usePost';

import moment from 'moment';
import Calendar from '@/components/common/calendar';
import UploadComponent from '@/components/common/uploadComponent';
import { IAttachment } from '@/interfaces/misc';
import { useAuthStore } from '@/globalProvider/authStore';
import { toast } from 'react-toastify';
import { useDropzone } from 'react-dropzone';
import { Upload } from 'lucide-react';
import { FileCard } from '@/components/common/fileCard';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';

const assessmentMethods = [
  { value: 'Self Assesment', label: 'Self Assesment' },
  { value: 'Manager Review', label: 'Manager Review' },
  { value: 'Peer Review', label: 'Peer Review' },
  { value: 'Formal Assesment', label: 'Formal Assesment' },
];

interface AssignSkillModalProps {
  employeeId: string;
  skillId: string;
  employeeName?: string;
  skillName?: string;
  onClose: () => void;
  skillLevels: any[];
  skillsData: any;
  reFetch: () => void;
  open: boolean;
}

const accepted_file_types: any = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
  ],
  'application/msword': ['.doc'],
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/png': ['.png'],
};

const AssignSkillModal = ({
  employeeId,
  skillId,
  employeeName,
  skillName,
  onClose,
  skillLevels = [],
  skillsData = { records: [] },
  reFetch,
  open = true,
}: AssignSkillModalProps) => {
  const [currentLevel, setCurrentLevel] = useState('');
  const [targetLevel, setTargetLevel] = useState('');
  const [assessor, setAssessor] = useState('');
  const [method, setMethod] = useState('');
  const [evidence, setEvidence] = useState('');
  const [notes, setNotes] = useState('');
  const [nextReviewDate, setNextReviewDate] = useState('');
  const [addedFiles, setAddedFiles] = useState<File[]>([]);
  const [trainingIds, setTrainingIds] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const { postData, response, isLoading, error } = usePost();
  const accessToken = useAuthStore((state) => state.accessToken);

  // Get current skill and employee information
  const currentSkillRecord = useMemo(() => {
    return skillsData?.records?.find(
      (record: any) =>
        record.employee_id === employeeId && record.skill_id === skillId,
    );
  }, [skillsData, employeeId, skillId]);

  // Use fallback values if record doesn't exist (skill not assigned yet)
  const finalEmployeeName =
    currentSkillRecord?.employee_name || employeeName || 'Unknown Employee';
  const finalSkillName =
    currentSkillRecord?.skill_name || skillName || 'Unknown Skill';
  const currentSkillLevelName = currentSkillRecord?.current_skill_level_name;

  // Set initial current level when component mounts or when current skill record changes
  useEffect(() => {
    if (currentSkillLevelName && skillLevels?.length > 0) {
      // Find the skill level object that matches the current level name
      const currentLevelObj = skillLevels.find(
        (level) => level.name === currentSkillLevelName,
      );
      if (currentLevelObj) {
        setCurrentLevel(currentLevelObj.id);
      }
    }
  }, [currentSkillLevelName, skillLevels]);

  const handleFileUpload = async (
    file: File,
  ): Promise<{ file_path: string; file_extension: string } | null> => {
    try {
      setIsUploading(true);
      const formData = new FormData();
      formData.append('file', file);

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=people_hub&sub_path=/employees/${employeeId}/${Date.now()}/documents/skills/${skillId}`;
      const response = await axios.post(url, formData, config);

      setIsUploading(false);
      return {
        file_path: response.data.file_path,
        file_extension: response.data.file_ext,
      };
    } catch (error) {
      setIsUploading(false);
      toast.error('File upload failed');
      return null;
    }
  };

  const handleSave = async () => {
    let response;

    if (addedFiles.length > 0) {
      const result = await handleFileUpload(addedFiles[0]);
      if (result) {
        response = result;
      }
    }

    const currentLevelObj = skillLevels.find(
      (level) => level.id === currentLevel,
    );
    const targetLevelObj = skillLevels.find(
      (level) => level.name === targetLevel,
    );

    const payload = {
      employee_id: employeeId,
      current_level_id: currentLevel || currentLevelObj?.id,
      target_level_id: targetLevelObj?.id,
      assessor_name: assessor,
      assessment_method: method,
      evidence: {
        evidence_type: 'document',
        documents: response
          ? [
              {
                file_path: response?.file_path,
                file_extension: response?.file_extension,
              },
            ]
          : [],
        training_ids: trainingIds,
      },
      notes: notes,
      next_review_date: nextReviewDate,
    };

    await postData(
      accessToken as string,
      `employee/skills/${skillId}/assign`,
      payload,
    );
  };

  // Get available skill levels for target (excluding current if it exists)
  const availableTargetLevels = (skillLevels || []).filter(
    (level) => level.id !== currentLevel,
  );

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setAddedFiles([acceptedFiles[0]]);
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: accepted_file_types,
    multiple: false,
  });

  const removeFile = () => {
    setAddedFiles([]);
  };

  useEffect(() => {
    if (response) {
      onClose();
      setAddedFiles([]);
      setTrainingIds([]);
      setNotes('');
      setNextReviewDate('');
      setTargetLevel('');
      setAssessor('');
      setMethod('');
      setEvidence('');
      setIsUploading(false);
      setCurrentLevel('');
      setTargetLevel('');
      toast.success('Skill assigned successfully');
      reFetch();
    }

    if (error) {
      toast.error('Failed to assign skill');
    }
  }, [response, error, onClose, reFetch]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Assign Skill</DialogTitle>
          {/* <div className="text-sm text-gray-600 mt-2">
            <p>
              <strong>Employee:</strong> {finalEmployeeName}
            </p>
            <p>
              <strong>Skill:</strong> {finalSkillName}
            </p>
            {currentSkillLevelName && (
              <p>
                <strong>Current Level:</strong> {currentSkillLevelName}
              </p>
            )}
            {!currentSkillLevelName && (
              <p className="text-orange-600 font-medium">
                This skill is not currently assigned to this employee
              </p>
            )}
          </div> */}
        </DialogHeader>

        <div className="space-y-5">
          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="current_level"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Current Level{' '}
              {currentSkillLevelName
                ? `(Currently: ${currentSkillLevelName})`
                : '(Not Assigned)'}
            </Label>
            <div className="flex flex-wrap gap-2">
              {(skillLevels || []).map((lvl) => {
                const levelNumber = lvl.name.replace('L', '');
                const colors = getSkillLevelColor(levelNumber);
                const isSelected = currentLevel === lvl.id;
                const isCurrentAssigned = lvl.name === currentSkillLevelName;

                return (
                  <button
                    key={lvl.id}
                    onClick={() => setCurrentLevel(lvl.id)}
                    className={`px-2.5 py-1.5 rounded-full border text-sm font-medium transition-colors relative ${
                      isSelected
                        ? `${colors.bg} text-white`
                        : `border-gray-300 hover:${colors.bg} hover:text-white`
                    } ${isCurrentAssigned ? 'ring-2 ring-blue-300' : ''}`}
                    title={`${getSkillLevelName(levelNumber)}${
                      isCurrentAssigned ? ' (Currently Assigned)' : ''
                    }`}
                  >
                    {lvl.name}
                    {isCurrentAssigned && (
                      <span className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white"></span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="target_level"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Target Level
            </Label>
            <div className="flex flex-wrap gap-2">
              {availableTargetLevels.map((lvl) => {
                const levelNumber = lvl.name.replace('L', '');
                const colors = getSkillLevelColor(levelNumber);
                const isSelected = targetLevel === lvl.name;

                return (
                  <button
                    key={lvl.id}
                    onClick={() => setTargetLevel(lvl.name)}
                    className={`px-2.5 py-1.5 rounded-full border text-sm font-medium transition-colors ${
                      isSelected
                        ? `${colors.bg} text-white`
                        : `border-gray-300 hover:${colors.bg} hover:text-white`
                    }`}
                    title={getSkillLevelName(levelNumber)}
                  >
                    {lvl.name}
                  </button>
                );
              })}
            </div>
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="assessor"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Assessor Name
            </Label>
            <Input
              id="assessor"
              placeholder="Enter assessor name"
              value={assessor}
              onChange={(e) => setAssessor(e.target.value)}
            />
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="method"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Assessment Method
            </Label>
            <Select value={method} onValueChange={setMethod}>
              <SelectTrigger>
                <SelectValue placeholder="Select assessment method" />
              </SelectTrigger>
              <SelectContent>
                {assessmentMethods.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="evidence"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Evidence
            </Label>
            <Textarea
              id="evidence"
              placeholder="Describe the evidence supporting this skill level"
              rows={3}
              value={evidence}
              onChange={(e) => setEvidence(e.target.value)}
            />
          </div>

          <div>
            <div className="flex flex-col gap-2.5">
              <div
                {...getRootProps()}
                className={`min-h-32 border-2 border-dashed rounded-lg flex items-center justify-center flex-col gap-2 p-6 cursor-pointer transition-colors ${
                  addedFiles.length > 0
                    ? 'bg-blue-50 border-blue-300'
                    : 'bg-gray-50 border-gray-300 hover:bg-gray-100'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="w-8 h-8 text-gray-400" />
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-700">
                    {addedFiles.length > 0
                      ? addedFiles[0].name
                      : 'Upload training content file'}
                  </p>
                  <p className="text-xs text-gray-500">
                    Drag and drop a file here, or click to select
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Supports PDF, DOC, DOCX, JPG, PNG, JPEG
                  </p>
                </div>
              </div>

              {addedFiles.length > 0 && (
                <div className="mt-4 space-y-2">
                  <Label className="text-sm font-medium">Selected File:</Label>
                  <FileCard
                    filepath={addedFiles[0].name}
                    file_extension={addedFiles[0].type}
                    handleDelete={() => removeFile()}
                  />
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="notes"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Notes
            </Label>
            <Textarea
              id="notes"
              placeholder="Additional notes or comments"
              rows={3}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>

          <div>
            <Label
              htmlFor="next_review_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Next Review Date
            </Label>
            <Calendar
              selectedDate={nextReviewDate}
              onDateChange={(date) => {
                if (date) {
                  setNextReviewDate(
                    moment(date as string).format('YYYY-MM-DD'),
                  );
                } else {
                  setNextReviewDate('');
                }
              }}
              allowPastDates={false}
              className="mt-2"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
          <SecondaryButton text="Cancel" size="medium" onClick={onClose} />
          <PrimaryButton
            text="Save"
            size="medium"
            isLoading={isLoading || isUploading}
            disabled={isLoading || isUploading}
            onClick={handleSave}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AssignSkillModal;
