import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/common/sidebar/layout';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import Status from '@/components/common/status';
import Tabs from '@/components/common/tabs';
import {
  Clock,
  FileText,
  CheckCircle,
  AlertCircle,
  Calendar,
  Users,
  Play,
  BookOpen,
} from 'lucide-react';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';

interface AssignedTraining {
  id: string;
  title: string;
  description: string;
  type: 'quiz' | 'pdf';
  status: 'open' | 'ongoing' | 'completed' | 'past_due';
  dueDate: string;
  assignedDate: string;
  completedDate?: string;
  duration: number;
  passingScore: number;
  category: string;
  progress: number;
  score?: number;
}

const MyTrainings = () => {
  const router = useRouter();
  const { accessToken, user } = useAuthStore();
  const [activeTab, setActiveTab] = useState(0);
  const [filter, setFilter] = useState<
    'all' | 'ongoing' | 'completed' | 'past_due'
  >('all');

  const breadcrumbData = [
    { name: 'People hub', link: '/people' },
    { name: 'My Trainings', link: '#' },
  ];

  const { data, isLoading, error, reFetch } = useFetch<any>(
    accessToken as string,
    `trainings/assignments/?user_id=${'c4236740-a813-4a32-bfaf-2335b0bfb622'}`,
    {},
  );
  const trainingData: AssignedTraining[] = data?.records
    ? data.records.map((rec: any) => ({
        id: rec.training.id,
        title: rec.training.title,
        description: rec.training.description,
        type: 'quiz', // fallback if API doesn't provide type
        status: (() => {
          if (rec.status === 'Assigned') return 'ongoing';
          if (rec.status === 'Completed') return 'completed';
          return 'past_due'; // fallback for any other status
        })(),
        dueDate: rec.due_date,
        assignedDate: rec.assigned_on,
        passingScore: 70,
        category: 'General',
        progress: rec.status === 'Assigned' ? 0 : 100,
      }))
    : [];

  const handleTabChange = (index: number) => {
    setActiveTab(index);
    const filterMap = ['all', 'ongoing', 'completed', 'past_due'] as const;
    setFilter(filterMap[index]);
  };

  const tabsData = [
    {
      name: `All Trainings (${trainingData.length})`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(0),
    },
    {
      name: `Ongoing (${
        trainingData.filter((t) =>
          ['not_started', 'in_progress', 'ongoing'].includes(t.status),
        ).length
      })`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(1),
    },
    {
      name: `Completed (${
        trainingData.filter((t) => t.status === 'completed').length
      })`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(2),
    },
    {
      name: `Past Due (${
        trainingData.filter((t) => t.status === 'past_due').length
      })`,
      textColor: 'text-[#E05252]',
      onClick: () => handleTabChange(3),
    },
  ];

  const filteredTrainings = trainingData.filter((training) => {
    if (filter === 'all') return true;
    if (filter === 'ongoing')
      return ['not_started', 'in_progress', 'ongoing'].includes(
        training.status,
      );
    if (filter === 'completed') return training.status === 'completed';
    if (filter === 'past_due') return training.status === 'past_due';
    return true;
  });

  const handleStartTraining = (training: AssignedTraining) => {
    if (training.type === 'quiz') {
      router.push(`/people/training/${training.id}/take-quiz`);
    } else if (training.type === 'pdf') {
      router.push(`/people/training/${training.id}/take-pdf-test`);
    } else {
      router.push(`/people/training/${training.id}`);
    }
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getActionButton = (training: AssignedTraining) => {
    if (training.status === 'completed') {
      return (
        <SecondaryButton
          text="View Results"
          size="medium"
          onClick={() =>
            router.push(`/people/training/${training.id}/pdf-results`)
          }
        />
      );
    }

    if (training.status === 'ongoing') {
      return (
        <PrimaryButton
          text="Continue"
          icon={<Play className="w-4 h-4" />}
          size="medium"
          onClick={() => handleStartTraining(training)}
        />
      );
    }

    return (
      <PrimaryButton
        text="Start Training"
        icon={<Play className="w-4 h-4" />}
        size="medium"
        onClick={() => handleStartTraining(training)}
      />
    );
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            My Trainings
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="mb-6 mt-2">
          <Tabs
            tabsData={tabsData}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            tabGroupName="my-trainings"
          />
        </div>

        {/* Training Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredTrainings.map((training) => (
            <div
              key={training.id}
              className={`border rounded-lg bg-white hover:shadow-md transition-shadow cursor-pointer ${
                training.status === 'past_due'
                  ? 'border-red-200'
                  : 'border-gray-200'
              }`}
              onClick={() => handleStartTraining(training)}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <span className="rounded-full text-xs capitalize font-bold px-3 py-1 bg-gray-100 text-gray-500">
                    {training.type.toUpperCase()}
                  </span>
                  <div className="flex items-center space-x-2">
                    <Status type={training.status} />
                  </div>
                </div>

                {/* Title and Description */}
                <h3 className="text-lg leading-5 font-medium text-black mb-2">
                  {training.title}
                </h3>
                <p className="text-base text-dark-300 leading-6 mb-4">
                  {training.description}
                </p>

                {/* Training Details */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className=" text-grey-300 text-base leading-6 font-medium">
                      Category:
                    </span>
                    <span className=" text-dark-300 text-base leading-6 font-medium">
                      {training.category}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className=" text-grey-300 text-base leading-6 font-medium ">
                      Due Date:
                    </span>
                    <span
                      className={`text-base leading-6 font-medium ${
                        isOverdue(training.dueDate) &&
                        training.status !== 'completed'
                          ? 'text-red-600'
                          : 'text-dark-300'
                      }`}
                    >
                      {formatDate(training.dueDate)}
                    </span>
                  </div>
                </div>

                {/* Action Button */}
                <div className="mt-6" onClick={(e) => e.stopPropagation()}>
                  {getActionButton(training)}
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredTrainings.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No trainings found
            </h3>
            <p className="text-gray-500">
              {filter === 'all'
                ? "You don't have any assigned trainings yet."
                : `No ${filter} trainings found.`}
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default MyTrainings;
