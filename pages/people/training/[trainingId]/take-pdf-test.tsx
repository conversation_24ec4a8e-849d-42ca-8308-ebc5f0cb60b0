import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/common/sidebar/layout';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import {
  Download,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
} from 'lucide-react';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { Label } from '@/components/common/label';
import { toast } from 'react-toastify';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import Link from 'next/link';
import Image from 'next/image';
import BackButton from '@/assets/backButton.svg';

interface PdfTrainingData {
  id: string;
  title: string;
  description: string;
  pdfUrl: string;
  fileName: string;
  fileSize: string;
  duration: number;
  category: string;
  passingScore: number;
  instructions: string;
  downloadCount: number;
  isDownloaded: boolean;
  submissionDeadline: string;
}

const TakePdfTest = () => {
  const router = useRouter();
  const { trainingId } = router.query;
  const { accessToken } = useAuthStore();
  const { postData, isLoading } = usePost();

  const [isDownloaded, setIsDownloaded] = useState(false);
  const [downloadCount, setDownloadCount] = useState(0);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);
  const [submissionNotes, setSubmissionNotes] = useState('');

  // Mock data - replace with actual API call
  const trainingData: PdfTrainingData = {
    id: trainingId as string,
    title: 'Emergency Response Procedures Manual',
    description:
      'Comprehensive guide to emergency response procedures, evacuation protocols, and safety measures.',
    pdfUrl: '/documents/emergency-response-manual.pdf',
    fileName: 'Emergency_Response_Manual_v2.1.pdf',
    fileSize: '2.4 MB',
    duration: 45,
    category: 'Safety',
    passingScore: 80,
    instructions: `
      Please read through the entire Emergency Response Procedures Manual carefully. 
      This document contains critical information about:
      
      • Emergency evacuation procedures
      • Fire safety protocols
      • Medical emergency response
      • Communication procedures during emergencies
      • Roles and responsibilities of emergency wardens
      
      After reviewing the document thoroughly, you can submit your completion. 
      You may download the PDF multiple times for reference.
    `,
    downloadCount: 0,
    isDownloaded: false,
    submissionDeadline: '2024-02-15T23:59:59Z',
  };

  const breadcrumbData = [
    { name: 'People hub', link: '/people' },
    { name: 'My Trainings', link: '/people/training/my-trainings' },
    { name: trainingData.title, link: `/people/training/${trainingId}` },
    { name: 'Take Test', link: '#' },
  ];

  const handleDownloadPdf = async () => {
    try {
      // Create a temporary link to download the PDF
      const link = document.createElement('a');
      link.href = trainingData.pdfUrl;
      link.download = trainingData.fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Update download status
      setIsDownloaded(true);
      setDownloadCount((prev) => prev + 1);

      // Track download in backend
      await postData(
        accessToken as string,
        `training-modules/${trainingId}/track-download`,
        {},
      );

      toast.success('PDF downloaded successfully');
    } catch (error) {
      toast.error('Failed to download PDF');
    }
  };

  const handleSubmitCompletion = async () => {
    if (!isDownloaded) {
      toast.error('Please download and review the PDF before submitting');
      return;
    }

    try {
      const payload = {
        trainingId,
        completionNotes: submissionNotes,
        downloadCount,
        completedAt: new Date().toISOString(),
      };

      await postData(
        accessToken as string,
        `training-modules/${trainingId}/submit-pdf-completion`,
        payload,
      );
      toast.success('Training completion submitted successfully!');
      router.push(`/people/training/${trainingId}/results`);
    } catch (error) {
      toast.error('Failed to submit completion. Please try again.');
    }
  };

  const isDeadlinePassed = () => {
    return new Date() > new Date(trainingData.submissionDeadline);
  };

  const formatDeadline = () => {
    return new Date(trainingData.submissionDeadline).toLocaleDateString(
      'en-US',
      {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      },
    );
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
            <Tooltip>
              <TooltipTrigger>
                <Link
                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                  href={`/people/training/my-trainings`}
                >
                  <Image src={BackButton} alt="" />
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm text-dark-300">Back</div>
              </TooltipContent>
            </Tooltip>
            {trainingData.title}
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Main Content - 75% */}
          <div className="xl:col-span-3">
            <div className="border border-gray-200 rounded-lg bg-white">
              {/* Header */}
              <div className="py-4 px-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-dark-300">
                    Training Instructions
                  </h2>
                  <div className="flex items-center space-x-2">
                    {isDownloaded && (
                      <div className="flex items-center text-green-600 text-sm">
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Downloaded
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Instructions */}
              <div className="pt-0 pb-6 px-6">
                <div className="prose max-w-none">
                  <div className="whitespace-pre-line text-gray-700 leading-relaxed">
                    {trainingData.instructions}
                  </div>
                </div>

                {/* PDF Download Section */}
                <div className="mt-8 p-6 bg-gray-50 rounded-lg">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-6 h-6 text-red-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-dark-300 mb-2">
                        {trainingData.fileName}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {trainingData.description}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                        <span>Size: {trainingData.fileSize}</span>
                        <span>•</span>
                        <span>
                          Estimated reading time: {trainingData.duration}{' '}
                          minutes
                        </span>
                        {downloadCount > 0 && (
                          <>
                            <span>•</span>
                            <span>
                              Downloaded {downloadCount} time
                              {downloadCount > 1 ? 's' : ''}
                            </span>
                          </>
                        )}
                      </div>
                      <div className="flex space-x-3">
                        <PrimaryButton
                          text="Download PDF"
                          icon={<Download className="w-4 h-4" />}
                          size="medium"
                          onClick={handleDownloadPdf}
                        />
                        <SecondaryButton
                          text="Preview"
                          icon={<Eye className="w-4 h-4" />}
                          size="medium"
                          onClick={() =>
                            window.open(trainingData.pdfUrl, '_blank')
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Submission Section */}
                {isDownloaded && (
                  <div className="mt-8 p-6 border border-gray-200 rounded-lg">
                    <h3 className="text-lg font-medium text-dark-300 mb-4">
                      Submit Training Completion
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Once you have thoroughly reviewed the training material,
                      you can submit your completion below.
                    </p>

                    <div className="mb-4">
                      <Label
                        htmlFor="notes"
                        className="text-sm font-medium text-gray-700"
                      >
                        Additional Notes (Optional)
                      </Label>
                      <textarea
                        id="notes"
                        rows={3}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Any questions, comments, or feedback about the training material..."
                        value={submissionNotes}
                        onChange={(e) => setSubmissionNotes(e.target.value)}
                      />
                    </div>

                    <div className="flex space-x-3">
                      <PrimaryButton
                        text="Submit Completion"
                        icon={<CheckCircle className="w-4 h-4" />}
                        size="medium"
                        onClick={() => setShowSubmitConfirm(true)}
                        isLoading={isLoading}
                      />
                      <SecondaryButton
                        text="Save Progress"
                        size="medium"
                        onClick={() => {
                          localStorage.setItem(
                            `pdf_training_progress_${trainingId}`,
                            JSON.stringify({
                              downloadCount,
                              isDownloaded,
                              notes: submissionNotes,
                              lastAccessed: new Date().toISOString(),
                            }),
                          );
                          toast.success('Progress saved');
                        }}
                      />
                    </div>
                  </div>
                )}

                {/* Deadline Warning */}
                {isDeadlinePassed() && (
                  <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                      <div>
                        <h4 className="text-red-800 font-medium">
                          Submission Deadline Passed
                        </h4>
                        <p className="text-red-700 text-sm mt-1">
                          The deadline for this training was {formatDeadline()}.
                          Please contact your supervisor.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar - 25% */}
          <div className="xl:col-span-1">
            <div className="border border-gray-200 rounded-lg bg-white sticky top-6">
              <div className="p-4 border-b border-gray-100">
                <h3 className="text-lg font-medium text-dark-300">
                  Training Details
                </h3>
              </div>
              <div className="p-4 space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Category
                  </Label>
                  <p className="text-dark-300 mt-1">{trainingData.category}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Estimated Duration
                  </Label>
                  <p className="text-dark-300 mt-1">
                    {trainingData.duration} minutes
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Submission Deadline
                  </Label>
                  <p
                    className={`mt-1 ${
                      isDeadlinePassed() ? 'text-red-600' : 'text-dark-300'
                    }`}
                  >
                    {formatDeadline()}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Status
                  </Label>
                  <div className="mt-1 flex items-center">
                    {isDownloaded ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="w-4 h-4 mr-1" />
                        <span className="text-sm">Material Downloaded</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-gray-500">
                        <Clock className="w-4 h-4 mr-1" />
                        <span className="text-sm">Not Started</span>
                      </div>
                    )}
                  </div>
                </div>

                {downloadCount > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">
                      Downloads
                    </Label>
                    <p className="text-dark-300 mt-1">
                      {downloadCount} time{downloadCount > 1 ? 's' : ''}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Confirmation Modal */}
        {showSubmitConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-medium text-dark-300 mb-4">
                Confirm Training Completion
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to submit this training as completed? This
                action cannot be undone.
              </p>
              <div className="flex space-x-3">
                <PrimaryButton
                  text="Yes, Submit"
                  size="medium"
                  onClick={handleSubmitCompletion}
                  isLoading={isLoading}
                />
                <SecondaryButton
                  text="Cancel"
                  size="medium"
                  onClick={() => setShowSubmitConfirm(false)}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default TakePdfTest;
