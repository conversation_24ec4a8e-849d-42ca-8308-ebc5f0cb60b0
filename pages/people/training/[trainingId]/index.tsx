import Breadcrumb from '@/components/common/breadcrumb';
import Layout from '@/components/common/sidebar/layout';
import BackButton from '@/assets/backButton.svg';
import {
  Users,
  CheckCircle,
  Calendar,
  Clock,
  FileText,
  Edit,
  Trash2,
  UserPlus,
  Download,
  Eye,
  ChevronDown,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import DeleteButton from '@/components/common/button/deleteButton';
import LinkButton from '@/components/common/button/linkButton';
import AssignEmployeesModal from '@/components/training/assignEmployeesModal';
import EditTrainingModal from '@/components/training/editTrainingModal';
import ManageQuizModal from '@/components/training/manageQuizModal';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import Status from '@/components/common/status';
import { DetailsText } from '@/components/common/infoDetail';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import Link from 'next/link';
import Image from 'next/image';
import useFetch from '@/hooks/useFetch';
import { useAuthStore } from '@/globalProvider/authStore';
import { useParams } from 'next/navigation';
import DocumentIcon from '@/assets/outline/document';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import DocumentViewModal from '@/components/common/modals/documentViewModal';
import { htmlToPdf } from '@/utils/htmlToPdf';
import { getFileNameFromPath } from '@/utils/helper';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios, { AxiosError } from 'axios';
import ConfirmModal from '@/components/common/modals/confirmModal';
import { usePost } from '@/hooks/usePost';
import { toast } from 'react-toastify';
import { useDelete } from '@/hooks/useDelete';
import DeleteModal from '@/components/common/modals/deleteModal';
import Loader from '@/components/common/loader';

interface ErrorResponse {
  error: string;
}

const TrainingView = () => {
  const router = useRouter();
  const { trainingId } = router.query;
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showQuizModal, setShowQuizModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [openDocumentModal, setOpenDocumentModal] = useState(false);
  const [openPublishModal, setOpenPublishModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);

  const { accessToken } = useAuthStore();

  type TrainingResponse = {
    record: any; // Replace 'any' with your actual training data type if available
  };

  const { data, isLoading, error, reFetch } = useFetch<TrainingResponse>(
    accessToken as string,
    `trainings/${trainingId}`,
    {},
  );

  const {
    postData: publishTraining,
    response: publishResponse,
    isLoading: publishLoading,
    error: publishError,
  } = usePost();

  const {
    data: questions,
    isLoading: questionsLoading,
    error: questionsError,
    reFetch: refetchQuestions,
  } = useFetch<any>(
    accessToken as string,
    `trainings/${trainingId}/questions`,
    {},
  );
  const {
    deleteData,
    response: deleteResponse,
    isLoading: deleteLoading,
    error: deleteError,
  } = useDelete();

  const trainingData = data?.record;

  const breadcrumbData = [
    {
      name: 'People hub',
      link: '/people',
    },
    {
      name: 'Training Admin',
      link: '/people/training',
    },
    { name: trainingData?.title || 'Loading...', link: '#' },
  ];

  useEffect(() => {
    if (publishResponse) {
      toast.success('Training published successfully');
      setOpenPublishModal(false);
      reFetch();
    }
    if (publishError) {
      toast.error('Failed to publish training');
      setOpenPublishModal(false);
    }
  }, [publishResponse, publishError]);

  useEffect(() => {
    if (deleteResponse) {
      toast.success('Training deleted successfully');
      setOpenDeleteModal(false);
      router.push('/people/training');
    }
    if (deleteError) {
      toast.error(
        ((deleteError as AxiosError).response?.data as ErrorResponse)?.error ||
          'An error occurred',
      );
      setOpenDeleteModal(false);
    }
  }, [deleteResponse, deleteError]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getTypeIcon = (type: string) =>
    type === 'quiz' ? <p>Quiz</p> : <FileText className="w-4 h-4" />;

  const completionPercentage = Math.round(
    ((trainingData?.completions || 0) /
      (trainingData?.assigned_user_count || 1)) *
      100,
  );

  const totalQuizPoints =
    trainingData?.training_version?.questions?.reduce(
      (total: number, question: any) => total + (question.points || 1),
      0,
    ) || 0;

  const renderQuizContent = () => (
    <Accordion type="single" collapsible className="w-full mt-2">
      <AccordionItem value="quiz-questions">
        <AccordionTrigger>
          <div className="flex items-center justify-between w-full mr-4">
            <div className="text-dark-300 text-base leading-6 py-1 font-medium">
              Quiz Questions
            </div>
            <div className="flex items-center space-x-2">
              <div className="rounded-full text-xs font-bold px-3 py-1 bg-primary-400 text-white">
                {questions?.records?.length || 0} Questions
              </div>
              <div className="rounded-full text-xs font-bold px-3 py-1 bg-blue-100 text-blue-600">
                {totalQuizPoints} Points Total
              </div>
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className=" pb-4 px-4 flex flex-col">
          <div className="space-y-4 pt-4">
            {(questions?.records || []).map((question: any, index: number) => (
              <div
                key={question.id}
                className="border border-gray-200 rounded-lg bg-white"
              >
                <div className="py-2 px-4 border-b border-gray-100">
                  <div className="flex items-start justify-between">
                    <div className="flex gap-3">
                      <p className="text-grey-300 font-medium text-base">
                        Question {index + 1}
                      </p>
                      <p className="text-black font-medium text-base">
                        {question.question_text}
                      </p>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="rounded-full text-xs font-bold px-2 py-1 bg-blue-100 text-blue-600">
                        {question.points}{' '}
                        {question.points === 1 ? 'point' : 'points'}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-4 space-y-3">
                  {question.question_type === 'MultipleChoice' && (
                    <div className="space-y-2">
                      <p className=" text-dark-300 text-base leading-6 font-medium mb-2">
                        Options:
                      </p>
                      <div className="space-y-1">
                        {question.options.map(
                          (option: any, optionIndex: any) => (
                            <div
                              key={optionIndex}
                              className="flex items-center space-x-2"
                            >
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  question.correct_answer === option
                                    ? 'bg-green-500'
                                    : 'bg-gray-300'
                                }`}
                              />
                              <span
                                className={`text-base text-dark-300 leading-6 ${
                                  question.correct_answer === option
                                    ? 'font-medium text-green-700'
                                    : 'text-gray-700'
                                }`}
                              >
                                {option}
                              </span>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  )}

                  {question.question_type === 'Boolean' && (
                    <div className="space-y-2">
                      <p className=" text-dark-300 text-base leading-6 font-medium mb-2">
                        Correct answer:
                      </p>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        <span className="text-base leading-6 font-medium text-green-700">
                          {question.correct_answer}
                        </span>
                      </div>
                    </div>
                  )}

                  {question.question_type === 'FillInBlank' && (
                    <div className="space-y-2">
                      <p className="text-dark-300 text-base leading-6 font-medium mb-2">
                        Fill in the blanks:
                      </p>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <div className="text-gray-700 leading-relaxed">
                          {question.question_text
                            .split('[...]')
                            .map((part: any, index: any) => (
                              <span key={index}>
                                {part}
                                {index <
                                  question.question_text.split('[...]').length -
                                    1 && (
                                  <span className="inline-block mx-1 px-2 py-1 bg-blue-100 text-blue-700 rounded border-b-2 border-blue-300">
                                    ___________
                                  </span>
                                )}
                              </span>
                            ))}
                        </div>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-gray-600 font-medium">
                          Correct answer:
                        </p>
                        <div className="flex items-center flex-wrap gap-2">
                          {question.correct_answer
                            ?.split('|')
                            .map((answer: string, index: number) => (
                              <span
                                key={index}
                                className="text-sm font-medium text-green-700 bg-green-50 px-2 py-1 rounded"
                              >
                                {answer.trim()}
                              </span>
                            ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {question.explanation && (
                    <div className="w-full bg-white-150 rounded-lg px-4 py-2">
                      <p className="text-base leading-6 font-medium">
                        <span className="text-grey-300">Explanation: </span>
                        {question?.explanation}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );

  if (isLoading) {
    return <Loader className="h-[400px]" />;
  }

  if (error) {
    return <p className="p-6 text-red-500">Failed to load training details.</p>;
  }

  if (!trainingData) {
    return <p className="p-6">No training found.</p>;
  }

  const handleDownloadDocument = (
    path: string,
    documentTitle: string,
    versionNumber: number,
    extension?: string,
  ) => {
    console.log('extension', path, documentTitle, versionNumber, extension);
    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const filePath = encodeURIComponent(path);
    const orgId =
      typeof window !== 'undefined'
        ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
        : null;
    axios
      .get(
        `${baseUrl}/${productVersion}/file/presigned-url?file_path=${filePath}&expiration=600`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          },
        },
      )
      .then((res) => {
        fetch(res.data.url)
          .then((res) => res.blob())
          .then((res) => {
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(res);
            link.download = `${documentTitle?.split('.')[0]}.${path
              .split('.')
              .pop()}`;

            link.click();
          });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handlePublishTraining = () => {
    async function fetch() {
      await publishTraining(
        accessToken as string,
        `trainings/${trainingId}`,
        {},
      );
    }
    fetch();
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
            <Tooltip>
              <TooltipTrigger>
                <Link
                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                  href={`/people/training`}
                >
                  <Image src={BackButton} alt="" />
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm text-dark-300">Back</div>
              </TooltipContent>
            </Tooltip>
            Training Details
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
          {/* Left Section - 80% */}
          <div className="xl:col-span-4 space-y-6">
            {/* Training Header */}
            <div className="">
              <div className="border border-grey-100 p-2 rounded-lg">
                <div className="px-2 py-3 mb-2 flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-3">
                      <h1 className="text-dark-300 font-semibold text-[1.55rem] leading-7 flex items-center gap-2.5">
                        {trainingData.title}
                      </h1>
                      <Status
                        type={trainingData.status.toLowerCase() as string}
                      />
                    </div>
                    <p className="text-lg text-grey-300 max-w-3xl">
                      {trainingData.description}
                    </p>
                  </div>
                </div>
                <div className="pt-2 border-t border-grey-50">
                  <div className="flex bg-white items-start gap-20 py-2 px-3">
                    <DetailsText
                      label="Type"
                      value={trainingData.training_type || '-'}
                      newLine
                    />
                    <DetailsText
                      label="Enrollments"
                      value={trainingData.assigned_user_count || '-'}
                      newLine
                    />
                    <DetailsText
                      label="Completions"
                      value="0" // Mock data since not in API
                      newLine
                    />
                  </div>
                </div>
              </div>
            </div>

            {trainingData?.training_version.file_path && (
              <div className="border rounded-md border-grey-50">
                {/* PDF Document Section */}
                <div className="p-6 bg-gray-50 rounded-lg">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-6 h-6 text-red-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-dark-300 mb-2">
                        {trainingData?.training_version?.file_path
                          ?.split('/')
                          .pop() || 'Training Document'}
                      </h3>

                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                        <span>
                          Type:{' '}
                          {trainingData?.training_version?.file_extension?.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex space-x-3">
                        <Dialog
                          open={openDocumentModal}
                          onOpenChange={setOpenDocumentModal}
                        >
                          <DialogTrigger asChild>
                            <PrimaryButton
                              text="View Document"
                              icon={<Eye className="w-4 h-4" />}
                              size="medium"
                            />
                          </DialogTrigger>
                          {openDocumentModal && (
                            <DocumentViewModal
                              title={trainingData?.title}
                              filePath={
                                trainingData?.training_version?.file_path
                              }
                              extension={
                                trainingData?.training_version?.file_extension
                              }
                              dialogClass="min-w-[95%]"
                            />
                          )}
                        </Dialog>
                        <SecondaryButton
                          text="Download"
                          icon={<Download className="w-4 h-4" />}
                          size="medium"
                          onClick={() =>
                            handleDownloadDocument(
                              trainingData?.training_version?.file_path,
                              trainingData?.title,
                              1,
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Training Content */}
            <div className="">
              <div className="">
                {renderQuizContent()}
                {/* {trainingData.training_type === 'quiz'
                  ? renderQuizContent()
                  : } */}
              </div>
            </div>

            {(trainingData.status.toLowerCase() as string) === 'draft' && (
              <Dialog
                open={openPublishModal}
                onOpenChange={setOpenPublishModal}
              >
                <DialogTrigger asChild>
                  <PrimaryButton
                    text="Publish Training"
                    size="medium"
                    onClick={() => setOpenPublishModal(true)}
                  />
                </DialogTrigger>
                <ConfirmModal
                  title={'Publish Training'}
                  infoText={'Are you sure you want to publish this training?'}
                  btnText={'Confirm'}
                  onClick={() => {
                    handlePublishTraining();
                  }}
                  btnLoading={publishLoading}
                  dialogClass="min-w-[45.438rem]"
                ></ConfirmModal>
              </Dialog>
            )}

            {/* <div className="">
              <div className="border border-grey-100 p-2 rounded-lg">
                <div className="px-2 py-3 mb-2 flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
                  <div className="space-y-2">
                    <h3 className="text-lg leading-5 font-medium text-black ">
                      Training Stats
                    </h3>
                    <p className="text-lg text-grey-300 max-w-3xl">
                      {trainingData.objectives}
                    </p>
                  </div>
                </div>
              </div>
            </div> */}
          </div>

          {/* Right Section - 20% */}
          <div className="xl:col-span-1 space-y-4">
            {/* Stats Card */}
            <div className="border border-gray-200 rounded-lg bg-white">
              <div className="p-4 border-b border-gray-100">
                <h3 className="text-lg leading-5 font-medium text-black ">
                  Training Stats
                </h3>
              </div>
              <div className="p-4 space-y-4">
                <div>
                  <div className="text-dark-100 text-base leading-6 font-medium mb-1">
                    Completion Rate
                  </div>
                  <div className="w-full bg-primary-200 h-3 rounded-full relative overflow-hidden">
                    <div
                      className={`absolute top-0 left-0 bg-primary-400 h-full rounded-full`}
                      style={{ width: `${completionPercentage}%` }}
                    ></div>
                  </div>

                  <p className="text-sm text-gray-500 mt-2">
                    {trainingData.completed_user_count || 0}/
                    {trainingData.assigned_user_count || 0} completed
                  </p>
                </div>

                <div className="border-t border-gray-100 pt-4">
                  <div className="flex flex-col gap-3">
                    <DetailsText
                      label="Version"
                      value={trainingData.training_version?.version || '-'}
                      newLine={true}
                    />
                    <DetailsText
                      label="Category"
                      value={trainingData.category?.name || '-'}
                      newLine={true}
                    />
                    <DetailsText
                      label="Language"
                      value="English" // Mock data since not in API
                      newLine={true}
                    />
                    <DetailsText
                      label="Supervisor"
                      value={trainingData.last_modified_by?.full_name || '-'}
                      newLine={true}
                    />
                  </div>
                </div>

                <div className="border-t border-gray-100 pt-4">
                  <DetailsText
                    label="Last Updated"
                    value={
                      trainingData.last_modified_by?.full_name +
                        ' on ' +
                        new Date(
                          trainingData.last_modified_on,
                        ).toLocaleDateString() || '-'
                    }
                    newLine={true}
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="border border-gray-200 rounded-lg bg-white">
              <div className="p-4 border-b border-gray-100">
                <h3 className="text-lg leading-5 font-medium text-black w-">
                  Actions
                </h3>
              </div>

              <div className="p-4 space-y-3">
                <PrimaryButton
                  text="Assign Employees"
                  icon={<UserPlus className="w-4 h-4" />}
                  size="medium"
                  onClick={() => setShowAssignModal(true)}
                  width="100%"
                />

                <SecondaryButton
                  text="Edit Training"
                  icon={<Edit className="w-4 h-4" />}
                  size="medium"
                  onClick={() => setShowEditModal(true)}
                  width="[100%]"
                />

                <SecondaryButton
                  text={
                    trainingData.training_type === 'document'
                      ? 'Manage Quiz'
                      : 'Edit PDF'
                  }
                  icon={<Edit className="w-4 h-4" />}
                  size="medium"
                  onClick={() => {
                    if (trainingData.training_type === 'document') {
                      setShowQuizModal(true);
                    } else {
                      console.log('Edit PDF functionality');
                    }
                  }}
                  width="[100%]"
                />

                <Dialog
                  open={openDeleteModal}
                  onOpenChange={setOpenDeleteModal}
                >
                  <DialogTrigger asChild>
                    <DeleteButton width="100%" text="Delete Training" />
                  </DialogTrigger>
                  <DeleteModal
                    title=""
                    infoText="Are you sure you want to delete this training? "
                    btnText="Delete"
                    onClick={() => {
                      async function fetch() {
                        await deleteData(
                          accessToken as string,
                          `trainings/${trainingId}`,
                        );
                      }
                      fetch();
                    }}
                    dialogContentClass="min-w-[33.375rem]"
                    btnLoading={deleteLoading}
                  />
                </Dialog>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <AssignEmployeesModal
        open={showAssignModal}
        onOpenChange={setShowAssignModal}
        trainingId={trainingId as string}
        trainingTitle={trainingData.title}
      />

      <EditTrainingModal
        open={showEditModal}
        onOpenChange={setShowEditModal}
        trainingId={trainingId as string}
        trainingData={trainingData}
        refetch={reFetch}
      />

      <ManageQuizModal
        open={showQuizModal}
        onOpenChange={setShowQuizModal}
        trainingId={trainingId as string}
        trainingTitle={trainingData.title}
        onSuccess={() => {
          refetchQuestions();
          reFetch(); // Refetch training data as well
        }}
        passing_score_in_percent={
          trainingData.training_version?.passing_score ?? 0
        }
        includeQuiz={
          trainingData.training_version?.passing_score > 0 ? true : false
        }
        trainingVersion={trainingData.training_version?.version || 1}
      />
    </Layout>
  );
};

export default TrainingView;
