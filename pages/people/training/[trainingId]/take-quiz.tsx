import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/common/sidebar/layout';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { CheckCircle, Clock, Download, Eye, FileText } from 'lucide-react';
import { toast } from 'react-toastify';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import Link from 'next/link';
import Image from 'next/image';
import BackButton from '@/assets/backButton.svg';
import { Input } from '@/components/common/input';
import useFetch from '@/hooks/useFetch';
import { handleDownloadDocument } from '@/utils/download';
import { Dialog } from '@radix-ui/react-dialog';
import { DialogTrigger } from '@/components/common/dialog';
import DocumentIcon from '@/assets/outline/document';
import DocumentViewModal from '@/components/common/modals/documentViewModal';

interface TrainingData {
  record: {
    id: string;
    title: string;
    description: string;
    duration: number;
    passingScore: number;
    training_version: {
      passing_score: number;
      file_path?: string;
      file_extension?: string;
    };
  };
}

interface QuestionsResponse {
  records: Question[];
}
interface Question {
  id: string;
  question_text: string;
  question_type: 'MultipleChoice' | 'Boolean' | 'ShortAnswer' | 'FillInBlank';
  points: number;
  correct_answer: string;
  sequence_number: number;
  options?: string[];
}

interface QuizAnswer {
  questionId: string;
  selectedAnswers: number[];
  textAnswer?: string;
  blankAnswers?: string[];
}

const TakeQuiz = () => {
  const router = useRouter();
  const { trainingId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);
  const { postData, isLoading } = usePost();

  const [answers, setAnswers] = useState<Record<string, QuizAnswer>>({});
  const [showValidation, setShowValidation] = useState(false);

  // Fetch questions from API
  const {
    data: questions,
    isLoading: questionsLoading,
    error: questionsError,
    reFetch: refetchQuestions,
  } = useFetch<QuestionsResponse>(
    accessToken as string,
    `trainings/${trainingId}/questions`,
    {},
  );

  const {
    data: trainingData,
    isLoading: trainingDataLoading,
    error: trainingDataError,
    reFetch: trainingDataReFetch,
  } = useFetch<TrainingData>(
    accessToken as string,
    `trainings/${trainingId}`,
    {},
  );

  const breadcrumbData = [
    { name: 'People hub', link: '/people' },
    { name: 'My Training', link: '/people/training/my-trainings' },
    {
      name: trainingData?.record?.title ?? '',
      link: `/people/training/${trainingId}`,
    },
    { name: 'Take Quiz', link: '#' },
  ];

  // Handle loading and error states
  if (questionsLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading questions...</div>
        </div>
      </Layout>
    );
  }

  if (questionsError || !questions?.records) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">
            Error loading questions. Please try again.
          </div>
        </div>
      </Layout>
    );
  }

  const questionsList = questions.records;

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleMultipleChoiceAnswer = (
    questionId: string,
    optionIndex: number,
  ) => {
    setAnswers((prev) => {
      return {
        ...prev,
        [questionId]: {
          questionId,
          selectedAnswers: [optionIndex], // Always single selection
        },
      };
    });
  };

  const handleTextAnswer = (questionId: string, text: string) => {
    setAnswers((prev) => {
      const newAnswers = { ...prev };

      if (text.trim().length > 0) {
        // Add or update answer
        newAnswers[questionId] = {
          questionId,
          selectedAnswers: [],
          textAnswer: text,
        };
      } else {
        // Remove answer if text is empty
        delete newAnswers[questionId];
      }

      return newAnswers;
    });
  };

  const handleBlankAnswer = (
    questionId: string,
    blankIndex: number,
    value: string,
  ) => {
    setAnswers((prev) => {
      const newAnswers = { ...prev };

      if (!newAnswers[questionId]) {
        newAnswers[questionId] = {
          questionId,
          selectedAnswers: [],
          blankAnswers: [],
        };
      }

      const blankAnswers = [...(newAnswers[questionId].blankAnswers || [])];
      blankAnswers[blankIndex] = value;

      newAnswers[questionId] = {
        ...newAnswers[questionId],
        blankAnswers,
      };

      return newAnswers;
    });
  };

  const isQuestionAnswered = (question: Question) => {
    const answer = answers[question.id];
    if (!answer) return false;

    if (question.question_type === 'ShortAnswer') {
      return answer.textAnswer && answer.textAnswer.trim().length > 0;
    } else if (question.question_type === 'FillInBlank') {
      const expectedBlanks =
        (question.correct_answer.match(/\|/g) || []).length + 1;
      return (
        answer.blankAnswers &&
        answer.blankAnswers.length === expectedBlanks &&
        answer.blankAnswers.every((blank) => blank && blank.trim().length > 0)
      );
    } else {
      return answer.selectedAnswers && answer.selectedAnswers.length > 0;
    }
  };

  const areAllQuestionsAnswered = () => {
    return questionsList.every((question) => isQuestionAnswered(question));
  };

  const getAnsweredQuestionsCount = () => {
    return questionsList.filter((question) => isQuestionAnswered(question))
      .length;
  };

  const getUnansweredQuestions = () => {
    return questionsList
      .map((question, index) => ({ question, index }))
      .filter(({ question }) => !isQuestionAnswered(question))
      .map(({ index }) => index + 1);
  };

  const handleSubmit = async () => {
    setShowValidation(true);

    if (!areAllQuestionsAnswered()) {
      const unansweredQuestions = getUnansweredQuestions();
      toast.error(
        `Please answer all questions. Missing: Question ${unansweredQuestions.join(
          ', ',
        )}`,
      );
      return;
    }

    const payload = {
      trainingId,
      answers: Object.values(answers),
    };

    try {
      await postData(
        accessToken as string,
        `training-modules/${trainingId}/submit-quiz`,
        payload,
      );
      toast.success('Quiz submitted successfully!');
      router.push(`/people/training/${trainingId}/results`);
    } catch (error) {
      toast.error('Failed to submit quiz. Please try again.');
    }
  };

  const handleSaveAndExit = () => {
    // Save current progress
    const payload = {
      trainingId,
      answers: Object.values(answers),
      isComplete: false,
    };

    // Save to localStorage or API
    localStorage.setItem(
      `quiz_progress_${trainingId}`,
      JSON.stringify(payload),
    );
    toast.info('Progress saved');
    router.push(`/people/training/${trainingId}`);
  };

  const handleDownload = (filePath: any, fileName: any) => {
    if (accessToken && filePath) {
      handleDownloadDocument(accessToken, filePath, fileName, 1);
    }
  };

  const renderQuestionWithBlanks = (question: Question) => {
    const parts = question.question_text.split('[...]');
    const expectedBlanks =
      (question.correct_answer.match(/\|/g) || []).length + 1;

    return (
      <div className="space-y-3">
        <div className="text-gray-700 leading-relaxed">
          {parts.map((part, index) => (
            <span key={index}>
              {part}
              {index < parts.length - 1 && (
                <span className="inline-block mx-2">
                  <Input
                    value={answers[question.id]?.blankAnswers?.[index] || ''}
                    onChange={(e) =>
                      handleBlankAnswer(question.id, index, e.target.value)
                    }
                    placeholder={`Answer ${index + 1}`}
                    className="inline-block w-32 h-8 text-center border-b-2 border-l-0 border-r-0 border-t-0 rounded-none focus:border-primary-500"
                  />
                </span>
              )}
            </span>
          ))}
        </div>

        {/* Show blank inputs separately for better mobile experience */}
        <div className="md:hidden space-y-2">
          <Label className="text-sm font-medium text-gray-600">
            Your answers:
          </Label>
          {Array.from({ length: expectedBlanks }, (_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 w-16">
                Blank {index + 1}:
              </span>
              <Input
                value={answers[question.id]?.blankAnswers?.[index] || ''}
                onChange={(e) =>
                  handleBlankAnswer(question.id, index, e.target.value)
                }
                placeholder="Your answer"
                className="flex-1"
              />
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />

          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
            <Tooltip>
              <TooltipTrigger>
                <Link
                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                  href={`/people/training/my-trainings`}
                >
                  <Image src={BackButton} alt="" />
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm text-dark-300">Back</div>
              </TooltipContent>
            </Tooltip>
            <div>
              <h1 className="text-dark-300 font-semibold text-[1.55rem] leading-7 flex items-center gap-2.5">
                {trainingData?.record?.title}
              </h1>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Quiz Content - 75% */}
          <div className="xl:col-span-3">
            {trainingData?.record?.training_version?.file_path && (
              <div className="p-6 mb-4 bg-gray-50 rounded-lg">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-6 h-6 text-red-600" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-dark-300 mb-1">
                      {trainingData?.record?.training_version?.file_path
                        .split('/')
                        .pop()}
                    </h3>

                    <p className="text-gray-600 mb-4">
                      {trainingData?.record?.description}
                    </p>

                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                      <span>
                        Type:{' '}
                        {trainingData?.record?.training_version?.file_extension}
                      </span>
                    </div>

                    <div className="flex space-x-3">
                      <PrimaryButton
                        text="Download PDF"
                        icon={<Download className="w-4 h-4" />}
                        size="medium"
                        onClick={() =>
                          handleDownload(
                            trainingData?.record?.training_version?.file_path,
                            trainingData?.record?.training_version?.file_path
                              .split('/')
                              .pop() as string,
                          )
                        }
                      />
                      <Dialog>
                        <DialogTrigger asChild>
                          {[
                            'pdf',
                            'docx',
                            'doc',
                            'jpg',
                            'png',
                            'jpeg',
                          ].includes(
                            trainingData?.record?.training_version
                              ?.file_extension as string,
                          ) && (
                            <SecondaryButton
                              text="Preview"
                              icon={<Eye className="w-4 h-4" />}
                              size="medium"
                            />
                          )}
                        </DialogTrigger>
                        <DocumentViewModal
                          title={'Test'}
                          filePath={
                            trainingData?.record?.training_version?.file_path
                          }
                          extension={
                            trainingData?.record?.training_version
                              ?.file_extension as
                              | 'pdf'
                              | 'docx'
                              | 'doc'
                              | 'png'
                              | 'jpeg'
                              | 'jpg'
                          }
                          dialogClass="min-w-[95%]"
                        />
                      </Dialog>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="border border-grey-100 rounded-lg bg-white">
              <div className="py-4 px-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-dark-300">
                    Questions
                  </h2>
                  <div className="text-sm text-gray-500">
                    {getAnsweredQuestionsCount()} of {questionsList.length}{' '}
                    answered
                  </div>
                </div>
              </div>

              <div className="p-6 space-y-4">
                {questionsList.map((question, index) => (
                  <div
                    key={question.id}
                    className={`border rounded-lg p-6 ${
                      showValidation && !isQuestionAnswered(question)
                        ? 'border-red-300 bg-red-50'
                        : isQuestionAnswered(question)
                        ? 'border-green-300 bg-green-50'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-600 rounded-full font-medium">
                          {index + 1}
                        </span>
                        <h3 className="text-lg font-medium text-dark-300">
                          {question.question_text}
                        </h3>
                      </div>
                      <div className="flex items-center space-x-2">
                        {isQuestionAnswered(question) && (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        )}
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {question.question_type
                            .replace(/([A-Z])/g, ' $1')
                            .trim()}
                        </span>
                      </div>
                    </div>

                    {/* Multiple Choice Questions */}
                    {question.question_type === 'MultipleChoice' &&
                      question.options && (
                        <div className="space-y-3">
                          {question.options.map((option, index) => (
                            <label
                              key={index}
                              className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                                answers[question.id]?.selectedAnswers?.includes(
                                  index,
                                )
                                  ? 'border-primary-500 bg-primary-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <input
                                type="radio"
                                name={`question-${question.id}`}
                                checked={
                                  answers[
                                    question.id
                                  ]?.selectedAnswers?.includes(index) || false
                                }
                                onChange={() =>
                                  handleMultipleChoiceAnswer(question.id, index)
                                }
                                className="w-4 h-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                              />
                              <span className="text-gray-700 flex-1">
                                {option}
                              </span>
                            </label>
                          ))}
                        </div>
                      )}

                    {/* Boolean Questions */}
                    {question.question_type === 'Boolean' &&
                      question.options && (
                        <div className="space-y-3">
                          <Label className="text-base font-medium text-dark-100">
                            Select True or False:
                          </Label>
                          <div className="space-y-2">
                            {question.options.map((option, optionIndex) => (
                              <label
                                key={optionIndex}
                                className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                              >
                                <input
                                  type="radio"
                                  name={`question_${question.id}`}
                                  checked={
                                    answers[
                                      question.id
                                    ]?.selectedAnswers?.includes(optionIndex) ||
                                    false
                                  }
                                  onChange={() =>
                                    handleMultipleChoiceAnswer(
                                      question.id,
                                      optionIndex,
                                    )
                                  }
                                  className="w-4 h-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                                />
                                <span className="text-dark-100">{option}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      )}

                    {/* Short Answer Questions */}
                    {question.question_type === 'ShortAnswer' && (
                      <div className="space-y-3">
                        <Label className="text-base font-medium text-dark-100">
                          Your Answer:
                        </Label>
                        <Textarea
                          value={answers[question.id]?.textAnswer || ''}
                          onChange={(e) =>
                            handleTextAnswer(question.id, e.target.value)
                          }
                          placeholder="Type your answer here..."
                          rows={4}
                          className="w-full"
                        />
                      </div>
                    )}

                    {/* Fill in Blanks Questions */}
                    {question.question_type === 'FillInBlank' && (
                      <div className="space-y-4">
                        <Label className="text-base font-medium text-dark-100">
                          Fill in the blanks:
                        </Label>
                        <div className="space-y-3">
                          {renderQuestionWithBlanks(question)}
                        </div>
                      </div>
                    )}

                    {showValidation && !isQuestionAnswered(question) && (
                      <p className="text-red-600 text-sm mt-2">
                        This question is required
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quiz Info Sidebar - 25% */}
          <div className="xl:col-span-1">
            <div className="border border-gray-200 rounded-lg bg-white sticky top-6">
              <div className="p-4 border-b border-gray-100">
                <h3 className="text-lg font-medium text-dark-300">
                  Quiz Information
                </h3>
              </div>

              <div className="p-4 space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Progress
                  </Label>
                  <div className="mt-2">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>
                        {getAnsweredQuestionsCount()} of {questionsList.length}
                      </span>
                      <span>
                        {Math.round(
                          (getAnsweredQuestionsCount() / questionsList.length) *
                            100,
                        )}
                        %
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${
                            (getAnsweredQuestionsCount() /
                              questionsList.length) *
                            100
                          }%`,
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Passing Score
                  </Label>
                  <p className="text-lg font-medium text-dark-300 mt-1">
                    {trainingData?.record?.training_version?.passing_score}%
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Total Questions
                  </Label>
                  <p className="text-lg font-medium text-dark-300 mt-1">
                    {questionsList.length}
                  </p>
                </div>
              </div>

              <div className="p-4 border-t border-gray-100 space-y-3">
                <PrimaryButton
                  text="Submit"
                  onClick={handleSubmit}
                  disabled={!areAllQuestionsAnswered()}
                  size="medium"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TakeQuiz;
