import { Target, Upload } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import BulkUploadModal from '@/components/people/bulkUploadModal';
import CreateEmployeeModal from '@/components/people/modals/createEmployeeModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TEmployeeData } from '@/interfaces/people';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { Book, GraduationCap, Settings2, UserIcon } from 'lucide-react';
import Card from '@/components/people/card';

const PeopleHub = () => {
  const breadcrumbData = [
    {
      name: 'People hub',
      link: '/people',
    },
  ];

  const data = [
    {
      id: 1,
      icon: <UserIcon color={'#016366'} width="32" height="32" />,
      title: 'Directory',
      description:
        'Manage employee profiles, departments, and organizational structure',
      link: 'directory',
      access: true,
    },
    {
      id: 2,
      icon: <GraduationCap color={'#016366'} width="32" height="32" />,
      title: 'Training Admin',
      description:
        'Assign training programs and track completion across the organization',
      link: 'training',
      access: true,
    },
    {
      id: 3,
      icon: <Book color={'#016366'} width="32" height="32" />,
      title: 'Trainings',
      description: 'Manage training programs and track completion',
      link: 'training/my-trainings',
      access: true,
    },
    {
      id: 3,
      icon: <Target color={'#016366'} width="32" height="32" />,
      title: 'Skills Management',
      description:
        'Manage skills, categories, and interactive skills matrix with gap analysis',
      link: 'skills-management',
      access: true,
    },
  ];

  return (
    <Layout>
      <div className=" my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            People Directory
          </div>
        </div>
        <div className="grid grid-cols-3 gap-5 mt-6">
          {data?.map((e, i) => (
            <Card key={i} data={e} />
          ))}
        </div>
      </div>
    </Layout>
  );
};

export default PeopleHub;
