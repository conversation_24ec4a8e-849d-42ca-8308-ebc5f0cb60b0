import { AccessActions } from '@/constants/access';
import { TCurrentUser } from '@/interfaces/user';

import * as role from '../constants/role';

const RoleAccessConfig: Record<AccessActions, string[]> = {
  [AccessActions.CanAddOrEditProducts]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.MASTER_PRODUCT_ADMIN,
    role.MASTER_PRODUCT_EDITOR,
  ],
  [AccessActions.CanEditSpecificProduct]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.MASTER_PRODUCT_ADMIN,
    role.MASTER_PRODUCT_EDITOR,
  ],
  [AccessActions.CanDeleteSpecificProduct]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.MASTER_PRODUCT_ADMIN,
  ],
  [AccessActions.CanAddOrEditAssets]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.ASSET_ADMIN,
    role.ASSET_EDITOR,
  ],
  [AccessActions.IsAssetAdmin]: [
    role.ADMI<PERSON>,
    role.SUPER_ADMIN,
    role.ASSET_ADMIN,
  ],
  [AccessActions.CanAddOrEditInventory]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.INVENTORY_ADMIN,
    role.INVENTORY_EDITOR,
  ],
  [AccessActions.IsInventoryAdmin]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.INVENTORY_ADMIN,
  ],
  [AccessActions.CanAddOrEditPeople]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.PEOPLE_ADMIN,
    role.PEOPLE_EDITOR,
  ],
  [AccessActions.IsPeopleAdmin]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.PEOPLE_ADMIN,
  ],
  [AccessActions.IsDocumentAdmin]: [
    role.SUPER_ADMIN,
    role.ADMIN,
    role.DOCUMENT_ADMIN,
  ],
  [AccessActions.CanEditDocument]: [
    role.SUPER_ADMIN,
    role.ADMIN,
    role.DOCUMENT_ADMIN,
    role.DOCUMENT_EDITOR,
  ],
  [AccessActions.IsDocumentEditor]: [
    role.ADMIN,
    role.DOCUMENT_ADMIN,
    role.DOCUMENT_EDITOR,
  ],
  [AccessActions.IsDocumentViewer]: [role.DOCUMENT_VIEW],
  [AccessActions.CanUploadDocument]: [
    role.SUPER_ADMIN,
    role.ADMIN,
    role.DOCUMENT_ADMIN,
  ],
  [AccessActions.CanLinkDocument]: [
    role.SUPER_ADMIN,
    role.ADMIN,
    role.STANDARD_EDITOR,
    role.STANDARD_ADMIN,
  ],
  [AccessActions.CanEditScope]: [
    role.SUPER_ADMIN,
    role.ADMIN,
    role.STANDARD_ADMIN,
  ],
  [AccessActions.CanAddOrEditWorkOrders]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.PRODUCTION_HUB_ADMIN,
    role.PRODUCTION_HUB_EDITOR,
  ],
  [AccessActions.CanEditSpecificWorkOrder]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.PRODUCTION_HUB_ADMIN,
    role.PRODUCTION_HUB_EDITOR,
  ],
  [AccessActions.CanDeleteSpecificWorkOrder]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.PRODUCTION_HUB_ADMIN,
  ],
  [AccessActions.IsAuditor]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.AUDIT_EDITOR,
    role.AUDIT_ADMIN,
  ],
  [AccessActions.IsAuditee]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.AUDIT_EDITOR,
    role.AUDIT_ADMIN,
  ],
  [AccessActions.CanCreateAudit]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.AUDIT_EDITOR,
    role.AUDIT_ADMIN,
  ],
  [AccessActions.CanEditDeleteCloseStartAudit]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.AUDIT_ADMIN,
    role.AUDIT_EDITOR,
  ],
  [AccessActions.ShowSidebarDocument]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.DOCUMENT_ADMIN,
    role.DOCUMENT_EDITOR,
    role.DOCUMENT_VIEW,
  ],
  [AccessActions.ShowSidebarStandard]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.STANDARD_ADMIN,
    role.STANDARD_EDITOR,
    role.STANDARD_VIEW,
  ],
  [AccessActions.ShowSidebarPeople]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.PEOPLE_ADMIN,
    role.PEOPLE_EDITOR,
    role.PEOPLE_VIEW,
  ],
  [AccessActions.ShowSidebarAsset]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.ASSET_ADMIN,
    role.ASSET_EDITOR,
    role.ASSET_VIEW,
  ],
  [AccessActions.ShowSidebarInventory]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.INVENTORY_ADMIN,
    role.INVENTORY_EDITOR,
    role.INVENTORY_VIEW,
  ],
  [AccessActions.ShowSidebarMaster]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.MASTER_PRODUCT_ADMIN,
    role.MASTER_PRODUCT_EDITOR,
    role.MASTER_PRODUCT_VIEW,
  ],
  [AccessActions.ShowSidebarProduction]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.PRODUCTION_HUB_ADMIN,
    role.PRODUCTION_HUB_EDITOR,
    role.PRODUCTION_HUB_VIEW,
  ],
  [AccessActions.ShowSidebarImprovement]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.IMPROVEMENT_HUB_ADMIN,
    role.IMPROVEMENT_HUB_EDITOR,
    role.IMPROVEMENT_HUB_VIEW,
  ],
  [AccessActions.ShowSidebarAudit]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.AUDIT_ADMIN,
    role.AUDIT_EDITOR,
    role.AUDIT_VIEW,
  ],
  [AccessActions.ShowSidebarVendor]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.VENDOR_HUB_ADMIN,
    role.VENDOR_HUB_EDITOR,
    role.VENDOR_HUB_VIEW,
  ],
  [AccessActions.VendorAdmin]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.VENDOR_HUB_ADMIN,
  ],
  [AccessActions.VendorEditor]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.VENDOR_HUB_ADMIN,
    role.VENDOR_HUB_EDITOR,
  ],
  [AccessActions.CanCreateUser]: [role.ADMIN, role.SUPER_ADMIN],
  [AccessActions.VIEW_RISK]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.RISK_HUB_ADMIN,
    role.RISK_HUB_EDITOR,
    role.RISK_HUB_VIEW
  ],
  [AccessActions.CREATE_RISK]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.RISK_HUB_ADMIN,
    role.RISK_HUB_EDITOR
  ],
  [AccessActions.EDIT_RISK]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.RISK_HUB_ADMIN,
    role.RISK_HUB_EDITOR
  ],
  [AccessActions.DELETE_RISK]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.RISK_HUB_ADMIN
  ],
  [AccessActions.IsRiskAdmin]: [
    role.ADMIN,
    role.SUPER_ADMIN,
    role.RISK_HUB_ADMIN,
    role.RISK_HUB_EDITOR,
    role.RISK_HUB_VIEW
  ]
};

export const hasAccess = (
  action: keyof typeof RoleAccessConfig,
  user: TCurrentUser | null,
  additionalCheck?: boolean,
): boolean => {
  const expectedRoles = RoleAccessConfig[action];
  if (!user?.roles) return false;

  const hasRequiredRole = user.roles.some((role: string) =>
    expectedRoles?.includes(role),
  );

  // if (hasRequiredRole) {return true}
  return additionalCheck === false
    ? additionalCheck
    : hasRequiredRole
    ? true
    : false;
};
