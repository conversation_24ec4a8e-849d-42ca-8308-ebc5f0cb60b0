export interface Question {
  id: string;
  description: string;
  type:
    | 'multiple_choice'
    | 'true_false'
    | 'short_answer'
    | 'fill_in_the_blanks';
  options: string[];
  correctAnswers: number[];
  sampleAnswer?: string;
  explanation?: string;
  blanks?: string[];
  points: number;
}

export interface QuizAnswer {
  questionId: string;
  selectedAnswers: number[];
  textAnswer?: string;
  blankAnswers?: string[];
}

export const calculateTotalPoints = (questions: Question[]): number => {
  return questions.reduce((total, question) => total + question.points, 0);
};

export const calculateScore = (
  questions: Question[],
  answers: Record<string, QuizAnswer>,
): number => {
  let earnedPoints = 0;

  questions.forEach((question) => {
    const answer = answers[question.id];
    if (!answer) return;

    let isCorrect = false;

    switch (question.type) {
      case 'multiple_choice':
      case 'true_false':
        // For single selection, check if the selected answer matches the correct answer
        isCorrect =
          question.correctAnswers.length === 1 &&
          answer.selectedAnswers.length === 1 &&
          question.correctAnswers[0] === answer.selectedAnswers[0];
        break;

      case 'short_answer':
        isCorrect =
          answer.textAnswer?.toLowerCase().trim() ===
          question.sampleAnswer?.toLowerCase().trim();
        break;

      case 'fill_in_the_blanks':
        isCorrect =
          (question.blanks?.length === answer.blankAnswers?.length &&
            question.blanks?.every(
              (correct, index) =>
                answer.blankAnswers?.[index]?.toLowerCase().trim() ===
                correct.toLowerCase().trim(),
            )) ||
          false;
        break;
    }

    if (isCorrect) {
      earnedPoints += question.points;
    }
  });

  return earnedPoints;
};

export const calculatePercentage = (
  earnedPoints: number,
  totalPoints: number,
): number => {
  if (totalPoints === 0) return 0;
  return Math.round((earnedPoints / totalPoints) * 100);
};

export const renderQuestionWithBlanks = (
  questionText: string,
): { parts: string[]; blankCount: number } => {
  const parts = questionText.split('[blank]');
  return {
    parts,
    blankCount: parts.length - 1,
  };
};
