import axios from 'axios';

import { getFileNameFromPath } from './helper';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

export const handleDownloadDocument = (
  accessToken: string,
  path: string,
  documentTitle: string,
  versionNumber: number,
) => {
  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;
  const filePath = encodeURIComponent(path);
  const orgId =
    typeof window !== 'undefined'
      ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
      : null;
  axios
    .get(
      `${baseUrl}/${productVersion}/file/presigned-url?file_path=${filePath}&expiration=600`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      },
    )
    .then((res) => {
      fetch(res.data.url)
        .then((res) => res.blob())
        .then((res) => {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(res);
          link.download = getFileNameFromPath(
            path,
            documentTitle,
            versionNumber,
          );
          link.click();
        });
    })
    .catch((err) => {
      console.log(err);
    });
};
